{"name": "tunshuedu-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@heroicons/vue": "^2.0.18", "@types/sortablejs": "^1.15.8", "@vueuse/core": "^10.7.0", "axios": "^1.10.0", "element-plus": "^2.10.2", "pinia": "^2.1.7", "quill": "^2.0.3", "sass": "^1.89.2", "sortablejs": "^1.15.6", "vue": "^3.5.16", "vue-quill-editor": "^3.0.6", "vue-router": "^4.5.1"}, "devDependencies": {"@stagewise/toolbar-vue": "^0.1.2", "@vitejs/plugin-vue": "^5.2.4", "autoprefixer": "^10.4.16", "postcss": "^8.5.6", "tailwindcss": "^3.3.5", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "vite": "^6.3.5"}}