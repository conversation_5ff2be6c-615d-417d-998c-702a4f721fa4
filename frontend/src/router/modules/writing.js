// 文书写作路由
export default [
  {
    path: 'write/recommendation',
    name: 'WriteRecommendation',
    component: () => import('@/views/writing/RL.vue'),
    meta: {
      title: '写推荐信',
      requiresAuth: true,
      fullscreen: true
    }
  },
  {
    path: 'write/cv',
    name: 'WriteCV',
    component: () => import('@/views/writing/CV.vue'),
    meta: {
      title: '写简历',
      requiresAuth: true,
      fullscreen: true
    }
  },
  {
    path: 'write/ps',
    name: 'WritePS',
    component: () => import('@/views/writing/PS.vue'),
    meta: {
      title: '写个人陈述',
      requiresAuth: true,
      fullscreen: true
    }
  }
]
