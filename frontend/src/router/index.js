import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// 导入路由模块
import authRoutes from './modules/auth'
import dashboardRoutes from './modules/dashboard'
import clientsRoutes from './modules/clients'
import schoolRoutes from './modules/school'
import writingRoutes from './modules/writing'
import aiToolsRoutes from './modules/ai-tools'
import accountRoutes from './modules/account'
import programsRoutes from './modules/programs'
import errorRoutes from './modules/error'

// 是否在控制台输出路由日志（可以根据环境变量控制）
const enableRouteLogging = process.env.NODE_ENV !== 'production'

const routes = [
  // 认证路由
  ...authRoutes,

  // 主应用路由
  {
    path: '/',
    component: () => import('@/components/layout/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/dashboard'
      },
      // 各功能模块路由
      ...dashboardRoutes,
      ...clientsRoutes,
      ...schoolRoutes,
      ...writingRoutes,
      ...aiToolsRoutes,
      ...programsRoutes,
      ...accountRoutes
    ]
  },

  // 错误页面路由（放在最后捕获所有未匹配的路径）
  ...errorRoutes
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)

  // 根据环境变量控制日志输出
  if (enableRouteLogging) {
    console.log(`[路由守卫] 导航到: ${to.path}, 需要认证: ${requiresAuth}, 令牌状态: ${authStore.token ? '存在' : '不存在'}`)
  }

  if (requiresAuth) {
    if (enableRouteLogging) {
      console.log('[路由守卫] 正在验证身份...')
    }

    try {
      const isAuthenticated = await authStore.checkAuth()

      if (enableRouteLogging) {
        console.log(`[路由守卫] 身份验证结果: ${isAuthenticated ? '通过' : '未通过'}`)
      }

      if (!isAuthenticated) {
        if (enableRouteLogging) {
          console.log('[路由守卫] 未认证，重定向到登录页')
        }
        next({ path: '/login', query: { redirect: to.fullPath } })
        return
      }
    } catch (error) {
      console.error('[路由守卫] 验证过程发生错误:', error)
      next({ path: '/login', query: { redirect: to.fullPath } })
      return
    }
  }

  // 如果已登录且访问登录页
  if (authStore.token && (to.path === '/login' || to.path === '/register' || to.path === '/forgot-password')) {
    if (enableRouteLogging) {
      console.log('[路由守卫] 已登录用户访问认证页面，重定向到仪表盘')
    }
    next('/dashboard')
    return
  }

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - TunshuEdu`
  } else {
    document.title = 'TunshuEdu - 留学行业的AI工具箱'
  }

  if (enableRouteLogging) {
    console.log('[路由守卫] 允许导航继续')
  }
  next()
})

export default router