<template>
  <div class="h-screen bg-white flex overflow-hidden">
    <!-- 左侧表单区域 -->
    <div class="w-96 flex-shrink-0 bg-white border-r border-gray-200 flex flex-col h-full">
      <div class="flex-1 overflow-y-auto p-6">
          <div class="space-y-6">
            <!-- 学生档案 -->
            <div>
              <div class="flex items-center justify-between mb-3">
                <label class="form-label text-red-500">
                  <span class="text-red-500 mr-1">*</span>学生档案
                </label>
                <button class="text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium">
                  + 新建档案
                </button>
              </div>
              <el-input
                v-model="formData.studentProfile"
                placeholder="请选择或搜索学生档案..."
                class="form-input"
                readonly
              >
                <template #suffix>
                  <span class="material-icons-outlined text-gray-400 text-lg">expand_more</span>
                </template>
              </el-input>
            </div>

            <!-- 申请院校 -->
            <div>
              <label class="form-label text-red-500">
                <span class="text-red-500 mr-1">*</span>申请院校
              </label>
              <el-select 
                v-model="formData.targetSchool" 
                placeholder="请选择或搜索申请院校..." 
                class="w-full form-input"
                filterable
              >
                <el-option label="哈佛大学" value="harvard"></el-option>
                <el-option label="斯坦福大学" value="stanford"></el-option>
                <el-option label="麻省理工学院" value="mit"></el-option>
                <el-option label="加州大学伯克利分校" value="berkeley"></el-option>
                <el-option label="牛津大学" value="oxford"></el-option>
                <el-option label="剑桥大学" value="cambridge"></el-option>
              </el-select>
            </div>

            <!-- 申请学位 -->
            <div>
              <label class="form-label text-red-500">
                <span class="text-red-500 mr-1">*</span>申请学位
              </label>
              <div class="flex space-x-3">
                <button 
                  @click="formData.degree = 'bachelor'"
                  :class="[
                    'flex-1 py-2.5 px-4 rounded-lg text-sm font-medium transition-colors duration-200',
                    formData.degree === 'bachelor' 
                      ? 'bg-[#4F46E5] text-white shadow-sm' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-200'
                  ]"
                >
                  本科
                </button>
                <button 
                  @click="formData.degree = 'master'"
                  :class="[
                    'flex-1 py-2.5 px-4 rounded-lg text-sm font-medium transition-colors duration-200',
                    formData.degree === 'master' 
                      ? 'bg-[#4F46E5] text-white shadow-sm' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-200'
                  ]"
                >
                  硕士
                </button>
                <button 
                  @click="formData.degree = 'phd'"
                  :class="[
                    'flex-1 py-2.5 px-4 rounded-lg text-sm font-medium transition-colors duration-200',
                    formData.degree === 'phd' 
                      ? 'bg-[#4F46E5] text-white shadow-sm' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-200'
                  ]"
                >
                  博士
                </button>
              </div>
            </div>

            <!-- 申请专业 -->
            <div>
              <label class="form-label text-red-500">
                <span class="text-red-500 mr-1">*</span>申请专业
                <span class="material-icons-outlined text-gray-400 text-sm ml-1 cursor-help" title="选择专业信息">help_outline</span>
              </label>
              <el-select 
                v-model="formData.major" 
                placeholder="选择专业" 
                class="w-full form-input"
                filterable
              >
                <el-option label="计算机科学" value="computer_science"></el-option>
                <el-option label="商业管理" value="business_administration"></el-option>
                <el-option label="工程学" value="engineering"></el-option>
                <el-option label="经济学" value="economics"></el-option>
                <el-option label="心理学" value="psychology"></el-option>
                <el-option label="生物学" value="biology"></el-option>
              </el-select>
            </div>

            <!-- 文书类型 -->
            <div>
              <label class="form-label text-red-500">
                <span class="text-red-500 mr-1">*</span>文书类型
              </label>
              <div class="flex space-x-3">
                <button 
                  @click="formData.documentType = 'ps'"
                  :class="[
                    'flex-1 py-2.5 px-4 rounded-lg text-sm font-medium transition-colors duration-200',
                    formData.documentType === 'ps' 
                      ? 'bg-[#4F46E5] text-white shadow-sm' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-200'
                  ]"
                >
                  PS
                </button>
                <button 
                  @click="formData.documentType = 'sop'"
                  :class="[
                    'flex-1 py-2.5 px-4 rounded-lg text-sm font-medium transition-colors duration-200',
                    formData.documentType === 'sop' 
                      ? 'bg-[#4F46E5] text-white shadow-sm' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-200'
                  ]"
                >
                  SOP
                </button>
              </div>
            </div>



            <!-- 学生自身经历描述 -->
            <div>
              <label class="form-label text-red-500">
                <span class="text-red-500 mr-1">*</span>学生自身经历描述
                <button class="ml-2 text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium">
                  查看范例
                </button>
                <button class="ml-2 text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium">
                  上传
                </button>
              </label>
              <el-input
                v-model="formData.personalExperience"
                type="textarea"
                :rows="6"
                placeholder="1. 你为什么要选择这个专业？&#10;2. 你对该专业的哪个领域感兴趣？（举例说明）&#10;3. 你未来的职业/人生规划是什么？&#10;..."
                class="form-textarea"
              ></el-input>
            </div>

            <!-- 院校要求及其他说明 -->
            <div>
              <label class="form-label">
                院校要求及其他说明
                <button class="ml-2 text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium">
                  上传
                </button>
              </label>
              <el-input
                v-model="formData.schoolRequirements"
                type="textarea"
                :rows="4"
                placeholder="请输入院校要求和其他需要说明的信息"
                class="form-textarea"
              ></el-input>
            </div>

            <!-- 目标字数 -->
            <div>
              <div class="flex items-center justify-between mb-3">
                <label class="form-label mb-0">目标字数</label>
                <span class="text-sm font-medium text-[#4F46E5]">{{ getWordLimitDisplay(formData.wordLimit) }}</span>
              </div>
              <div class="px-2">
                <el-slider
                  v-model="formData.wordLimit"
                  :marks="wordLimitMarks"
                  :step="1"
                  :min="0"
                  :max="4"
                  :show-tooltip="false"
                  class="custom-slider"
                />
              </div>
            </div>

            <!-- 段落设置 -->
            <div>
              <div class="flex items-center justify-between mb-3">
                <label class="form-label mb-0">段落设置</label>
                <span class="text-sm font-medium text-[#4F46E5]">{{ getParagraphDisplay(paragraphValue) }}</span>
              </div>
              <div class="px-2">
                <el-slider
                  v-model="paragraphValue"
                  :marks="paragraphMarks"
                  :step="1"
                  :min="0"
                  :max="6"
                  :show-tooltip="false"
                  @input="handleParagraphInput"
                  @change="handleParagraphChange"
                  class="custom-slider"
                />
              </div>
            </div>

            <!-- 底部按钮区域 -->
            <div class="pt-6 border-t border-gray-100 mt-6">
              <button 
                @click="handleGeneratePS" 
                :disabled="isGenerating || !isFormValid"
                :class="[
                  'w-full py-3 px-4 rounded-lg font-medium text-white transition-colors duration-200 flex items-center justify-center',
                  isFormValid && !isGenerating
                    ? 'bg-[#4F46E5] hover:bg-[#4338CA] shadow-sm cursor-pointer'
                    : 'bg-gray-400 cursor-not-allowed'
                ]"
              >
                <span v-if="isGenerating">
                  <span class="material-icons-outlined animate-spin mr-2 text-lg">refresh</span>
                  生成中...
                </span>
                <span v-else>
                  <span class="material-icons-outlined mr-2 text-lg">auto_fix_high</span>
                  提交生成
                </span>
              </button>
            </div>
          </div>
        </div>
    </div>

    <!-- 右侧编辑器区域 -->
    <div class="flex-1 bg-white">
      <SharedTextEditor
        v-model="psContent"
        document-type="ps"
        placeholder="开始编写您的PS，或点击左侧'提交生成'获取AI建议..."
        @save="handleSave"
        @export="handleExport"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElSlider } from 'element-plus'
import SharedTextEditor from '@/components/writing/SharedTextEditor.vue'

// 表单数据
const formData = reactive({
  studentProfile: '',
  targetSchool: '',
  degree: 'master',
  major: '',
  documentType: 'ps',

  personalExperience: '',
  schoolRequirements: '',
  wordLimit: 0, // 滑动器数值：0=不限制, 1=500, 2=1000, 3=1500, 4=2000
  paragraphSetting: 0 // 滑动器数值：0=不限制, 1=智能分段, 2=4段, 3=5段, 4=6段, 5=7段, 6=8段
})

// 目标字数滑动器标记
const wordLimitMarks = {
  0: '不限制',
  1: '500',
  2: '1000', 
  3: '1500',
  4: '2000'
}

// 段落设置滑动器标记
const paragraphMarks = {
  0: '不限制',
  1: '智能分段',
  2: '4段',
  3: '5段',
  4: '6段',
  5: '7段',
  6: '8段'
}

// PS内容
const psContent = ref('')
const isGenerating = ref(false)

// 独立的段落设置变量
const paragraphValue = ref(0)

// 获取字数限制显示文本
const getWordLimitDisplay = (value) => {
  const displays = ['不限制', '500词', '1000词', '1500词', '2000词']
  return displays[value] || '不限制'
}

// 获取段落设置显示文本
const getParagraphDisplay = (value) => {
  const displays = ['不限制', '智能分段', '4段', '5段', '6段', '7段', '8段']
  return displays[value] || '不限制'
}

// 段落设置输入处理
const handleParagraphInput = (value) => {
  console.log('段落设置实时变化:', value)
  formData.paragraphSetting = value
}

// 段落设置变化处理
const handleParagraphChange = (value) => {
  console.log('段落设置变化完成:', value, getParagraphDisplay(value))
  formData.paragraphSetting = value
}

// 验证表单是否有效
const isFormValid = computed(() => {
  return formData.studentProfile && 
         formData.targetSchool && 
         formData.degree && 
         formData.major && 
         formData.documentType && 
         formData.personalExperience
})

// 生成PS初稿
const handleGeneratePS = async () => {
  if (!isFormValid.value) {
    ElMessage.warning('请完成所有必填项目')
    return
  }

  isGenerating.value = true
  try {
    ElMessage.success('正在生成PS初稿，请稍候...')
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 生成的PS模板
    const generatedPS = generatePSTemplate()
    psContent.value = generatedPS
    
    ElMessage.success('PS初稿生成完成，您可以继续编辑')
  } catch (error) {
    ElMessage.error('生成失败，请重试')
    console.error('Generate PS error:', error)
  } finally {
    isGenerating.value = false
  }
}

// 生成PS模板
const generatePSTemplate = () => {
  const currentDate = new Date().toLocaleDateString('zh-CN')
  const docTypeTitle = formData.documentType === 'ps' ? 'Personal Statement' : 'Statement of Purpose'
  const wordLimitText = getWordLimitDisplay(formData.wordLimit)
  const paragraphText = getParagraphDisplay(formData.paragraphSetting)
  
  return `
    <div style="margin-bottom: 30px; color: #374151;">
      <p style="margin-bottom: 20px; font-weight: 500; color: #4F46E5; font-size: 16px; text-align: center;">
        ${docTypeTitle}
      </p>
      <p style="margin-bottom: 10px;">日期: ${currentDate}</p>
      <p style="margin-bottom: 10px;">申请院校: <strong style="color: #4F46E5;">${formData.targetSchool}</strong></p>
      <p style="margin-bottom: 10px;">申请学位: <strong style="color: #4F46E5;">${formData.degree === 'master' ? '硕士' : formData.degree === 'phd' ? '博士' : '学士'}</strong></p>
      <p style="margin-bottom: 10px;">申请专业: <strong style="color: #4F46E5;">${formData.major}</strong></p>
      <p style="margin-bottom: 10px;">文书类型: <strong style="color: #4F46E5;">${formData.documentType.toUpperCase()}</strong></p>
      <p style="margin-bottom: 10px;">目标字数: ${wordLimitText}</p>
      <p style="margin-bottom: 20px;">段落设置: ${paragraphText}</p>
    </div>
    
    <h1 style="text-align: center; font-size: 20px; font-weight: bold; margin-bottom: 30px; color: #1F2937;">
      ${docTypeTitle}
    </h1>
    
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      ${formData.personalExperience}
    </p>
    
    ${formData.schoolRequirements ? `
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      <strong>院校要求及其他说明：</strong><br>
      ${formData.schoolRequirements.replace(/\n/g, '<br>')}
    </p>
    ` : ''}
    
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      我对<strong style="color: #4F46E5;">${formData.major}</strong>专业的学术兴趣和热情使我坚定地选择了这个领域。通过深入的学习和实践，我相信自己具备了在贵校继续深造的能力和决心。
    </p>
    
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      我特别向往<strong style="color: #4F46E5;">${formData.targetSchool}</strong>，因为该校在<strong style="color: #4F46E5;">${formData.major}</strong>领域的卓越声誉和丰富的研究机会与我的学术目标完美契合。
    </p>
    
    <p style="line-height: 1.6; color: #374151;">
      我确信我的学术背景、实践经验和对该领域的热情使我成为贵校项目的优秀候选人。我期待为学术社区做出贡献，并向杰出的教师和同学学习。
    </p>
  `
}

// 保存处理
const handleSave = (data) => {
  console.log('Saving PS:', data)
  // 这里实现保存到后端的逻辑
}

// 导出处理
const handleExport = (data) => {
  console.log('Exporting PS:', data)
  // 这里实现导出功能的逻辑
}
</script> 

<style scoped>
/* 表单标签样式 */
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* 表单输入框样式 */
.form-input :deep(.el-input__wrapper) {
  @apply border-gray-300 rounded-lg shadow-sm;
}

.form-input :deep(.el-input__wrapper:focus-within) {
  @apply !border-[#4F46E5];
  box-shadow: 0 0 0 1px #4F46E5 inset !important;
}

.form-textarea :deep(.el-textarea__inner) {
  @apply border-gray-300 rounded-lg shadow-sm;
}

.form-textarea :deep(.el-textarea__inner:focus) {
  @apply !border-[#4F46E5];
  box-shadow: 0 0 0 1px #4F46E5 inset !important;
}

/* 选择框样式 */
.form-input :deep(.el-select) {
  @apply w-full;
}

.form-input :deep(.el-select__wrapper) {
  @apply border-gray-300 rounded-lg shadow-sm;
}

.form-input :deep(.el-select__wrapper:focus-within) {
  @apply !border-[#4F46E5];
  box-shadow: 0 0 0 1px #4F46E5 inset !important;
}

/* 移除了pro-card相关样式，现在使用直接布局 */



/* 单选按钮样式 */
input[type="radio"] {
  @apply w-4 h-4;
  accent-color: #4F46E5;
}

/* Material Icons */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  line-height: 1;
  transition: color 0.2s ease;
}

/* 按钮悬停效果 */
button:hover {
  @apply transform transition-transform duration-200;
}

/* 覆盖 Element Plus 主题色 */
:deep(.el-input) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-textarea) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-select) {
  --el-color-primary: #4F46E5 !important;
}

/* Element Plus 下拉选择框紫色主题 */
:deep(.el-select-dropdown__item.is-hovering) {
  background-color: rgba(79, 70, 229, 0.1) !important;
  color: #4F46E5 !important;
}

:deep(.el-select-dropdown__item.is-selected) {
  color: #4F46E5 !important;
  font-weight: 500 !important;
}

/* 滑动器样式 */
.custom-slider {
  margin: 15px 0 35px 0;
  height: 50px;
  position: relative;
}

.custom-slider :deep(.el-slider__runway) {
  background-color: #E5E7EB;
  height: 6px;
  border-radius: 3px;
  cursor: pointer;
}

.custom-slider :deep(.el-slider__bar) {
  background-color: #4F46E5;
  height: 6px;
  border-radius: 3px;
}

.custom-slider :deep(.el-slider__button) {
  background-color: #4F46E5;
  border: 2px solid #FFFFFF;
  width: 20px;
  height: 20px;
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.3);
  cursor: grab;
  transition: all 0.2s ease;
  position: relative;
  z-index: 10;
}

.custom-slider :deep(.el-slider__button:hover) {
  background-color: #4338CA;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.custom-slider :deep(.el-slider__button:active) {
  cursor: grabbing;
  transform: scale(0.95);
}

.custom-slider :deep(.el-slider__button-wrapper) {
  cursor: pointer;
  z-index: 10;
  position: relative;
}

.custom-slider :deep(.el-slider__marks) {
  top: 20px;
  position: absolute;
  width: 100%;
}

.custom-slider :deep(.el-slider__marks-text) {
  font-size: 12px;
  color: #6B7280;
  white-space: nowrap;
  cursor: pointer;
  user-select: none;
  position: absolute;
  transform: translateX(-50%);
  line-height: 1;
  margin-top: 5px;
}

.custom-slider :deep(.el-slider__marks .el-slider__marks-text:hover) {
  color: #4F46E5;
  font-weight: 500;
}

/* 确保滑动器可以正常交互 */
.custom-slider :deep(.el-slider) {
  pointer-events: all;
  height: 20px;
  position: relative;
}

.custom-slider :deep(.el-slider__runway) {
  margin: 0;
}

/* 强制确保滑动器交互 */
.custom-slider :deep(.el-slider__button-wrapper) {
  z-index: 999 !important;
  pointer-events: auto !important;
}

.custom-slider :deep(.el-slider__runway) {
  pointer-events: auto !important;
}

.custom-slider :deep(.el-slider) {
  z-index: 10 !important;
}

/* 禁用任何可能阻止交互的父级样式 */
.custom-slider {
  overflow: visible !important;
  z-index: 10;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .w-96 {
    @apply w-full;
  }
  
  .flex {
    @apply flex-col;
  }
  
  .h-screen {
    @apply min-h-screen;
  }
  
  /* 移动端调整 */
  .w-96 {
    @apply w-full;
  }
  
  .px-6 {
    @apply px-4;
  }
  
  .p-6 {
    @apply p-4;
  }
}
</style> 