<template>
  <div class="h-screen bg-white flex overflow-hidden">
    <!-- 左侧表单区域 -->
    <div class="w-96 flex-shrink-0 bg-white border-r border-gray-200 flex flex-col h-full">
      <div class="flex-1 overflow-y-auto p-6">
          <div class="space-y-6">
            <!-- 学生档案 -->
            <div>
              <div class="flex items-center justify-between mb-3">
                <label class="form-label text-red-500">
                  <span class="text-red-500 mr-1">*</span>学生档案
                </label>
                <button class="text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium">
                  + 新建档案
                </button>
              </div>
              <el-input
                v-model="formData.studentProfile"
                placeholder="请选择或搜索学生档案..."
                class="form-input"
                readonly
              >
                <template #suffix>
                  <span class="material-icons-outlined text-gray-400 text-lg">expand_more</span>
                </template>
              </el-input>
            </div>

            <!-- 简历语言 -->
            <div>
              <label class="form-label">简历语言</label>
              <div class="flex space-x-3">
                <button 
                  @click="formData.language = 'english'"
                  :class="[
                    'flex-1 py-2.5 px-4 rounded-lg text-sm font-medium transition-colors duration-200',
                    formData.language === 'english' 
                      ? 'bg-[#4F46E5] text-white shadow-sm' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-200'
                  ]"
                >
                  英文
                </button>
                <button 
                  @click="formData.language = 'chinese'"
                  :class="[
                    'flex-1 py-2.5 px-4 rounded-lg text-sm font-medium transition-colors duration-200',
                    formData.language === 'chinese' 
                      ? 'bg-[#4F46E5] text-white shadow-sm' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-200'
                  ]"
                >
                  中文
                </button>
              </div>
            </div>

            <!-- 额外信息 -->
            <div>
              <label class="form-label">
                额外信息
                <button class="ml-2 text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium">
                  查看范例
                </button>
              </label>
              <el-input
                v-model="formData.additionalInfo"
                type="textarea"
                :rows="6"
                placeholder="请输入学生档案之外的补充信息，且希望加入到简历内，例如：特殊经历、未包含在学生档案中的成就、个人情况说明等"
                class="form-textarea"
              ></el-input>
            </div>

            <!-- 底部按钮区域 -->
            <div class="pt-6 border-t border-gray-100 mt-6">
              <button 
                @click="handleGenerateCV" 
                :disabled="isGenerating || !isFormValid"
                :class="[
                  'w-full py-3 px-4 rounded-lg font-medium text-white transition-colors duration-200 flex items-center justify-center',
                  isFormValid && !isGenerating
                    ? 'bg-[#4F46E5] hover:bg-[#4338CA] shadow-sm cursor-pointer'
                    : 'bg-gray-400 cursor-not-allowed'
                ]"
              >
                <span v-if="isGenerating">
                  <span class="material-icons-outlined animate-spin mr-2 text-lg">refresh</span>
                  生成中...
                </span>
                <span v-else>
                  <span class="material-icons-outlined mr-2 text-lg">auto_fix_high</span>
                  提交生成
                </span>
              </button>
            </div>
          </div>
        </div>
    </div>

    <!-- 右侧编辑器区域 -->
    <div class="flex-1 bg-white">
      <SharedTextEditor
        v-model="cvContent"
        document-type="cv"
        placeholder="开始编写您的CV，或点击左侧'提交生成'获取AI建议..."
        @save="handleSave"
        @export="handleExport"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import SharedTextEditor from '@/components/writing/SharedTextEditor.vue'

// 表单数据
const formData = reactive({
  studentProfile: '',
  language: 'english',
  additionalInfo: ''
})

// CV内容
const cvContent = ref('')
const isGenerating = ref(false)

// 验证表单是否有效
const isFormValid = computed(() => {
  return formData.studentProfile.trim() !== ''
})

// 生成CV初稿
const handleGenerateCV = async () => {
  if (!isFormValid.value) {
    ElMessage.warning('请选择学生档案')
    return
  }

  isGenerating.value = true
  try {
    ElMessage.success('正在生成CV初稿，请稍候...')
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 生成的CV模板
    const generatedCV = generateCVTemplate()
    cvContent.value = generatedCV
    
    ElMessage.success('CV初稿生成完成，您可以继续编辑')
  } catch (error) {
    ElMessage.error('生成失败，请重试')
    console.error('Generate CV error:', error)
  } finally {
    isGenerating.value = false
  }
}

// 生成CV模板
const generateCVTemplate = () => {
  const isEnglish = formData.language === 'english'
  const currentDate = new Date().toLocaleDateString(isEnglish ? 'en-US' : 'zh-CN')
  
  if (isEnglish) {
    return `
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="font-size: 24px; font-weight: bold; margin-bottom: 10px; color: #1F2937;">
          [Your Name]
        </h1>
        <p style="color: #4F46E5; font-size: 16px; margin-bottom: 5px;">Curriculum Vitae</p>
        <p style="color: #6B7280; font-size: 14px;">Generated on: ${currentDate}</p>
      </div>
      
      <h2 style="color: #4F46E5; border-bottom: 2px solid #4F46E5; padding-bottom: 5px; margin-top: 25px; margin-bottom: 15px; font-size: 18px;">
        Personal Information
      </h2>
      <div style="color: #374151; margin-bottom: 20px;">
        <p style="margin-bottom: 8px;"><strong>Email:</strong> <EMAIL></p>
        <p style="margin-bottom: 8px;"><strong>Phone:</strong> +86 xxx-xxxx-xxxx</p>
        <p style="margin-bottom: 8px;"><strong>Address:</strong> [Your Address]</p>
        <p style="margin-bottom: 8px;"><strong>Student Profile:</strong> <span style="color: #4F46E5;">${formData.studentProfile}</span></p>
      </div>
      
      <h2 style="color: #4F46E5; border-bottom: 2px solid #4F46E5; padding-bottom: 5px; margin-top: 25px; margin-bottom: 15px; font-size: 18px;">
        Education
      </h2>
      <div style="color: #374151; margin-bottom: 20px;">
        <p style="margin-bottom: 8px;"><strong>[University Name]</strong></p>
        <p style="margin-bottom: 8px;">[Degree] in [Major] | GPA: X.XX/4.0</p>
        <p style="margin-bottom: 8px;">[Start Year] - [End Year]</p>
      </div>
      
      <h2 style="color: #4F46E5; border-bottom: 2px solid #4F46E5; padding-bottom: 5px; margin-top: 25px; margin-bottom: 15px; font-size: 18px;">
        Experience & Projects
      </h2>
      <div style="color: #374151; margin-bottom: 20px;">
        <p style="margin-bottom: 8px;">[Please add your internship, project, or research experience]</p>
      </div>
      
      <h2 style="color: #4F46E5; border-bottom: 2px solid #4F46E5; padding-bottom: 5px; margin-top: 25px; margin-bottom: 15px; font-size: 18px;">
        Skills & Competencies
      </h2>
      <div style="color: #374151; margin-bottom: 20px;">
        <p style="margin-bottom: 8px;">[Please add your skills and competencies]</p>
      </div>
      
      ${formData.additionalInfo ? `
      <h2 style="color: #4F46E5; border-bottom: 2px solid #4F46E5; padding-bottom: 5px; margin-top: 25px; margin-bottom: 15px; font-size: 18px;">
        Additional Information
      </h2>
      <div style="color: #374151; margin-bottom: 20px;">
        <p style="line-height: 1.6;">${formData.additionalInfo.replace(/\n/g, '<br>')}</p>
      </div>
      ` : ''}
    `
  } else {
    return `
      <div style="text-align: center; margin-bottom: 30px;">
        <h1 style="font-size: 24px; font-weight: bold; margin-bottom: 10px; color: #1F2937;">
          [您的姓名]
        </h1>
        <p style="color: #4F46E5; font-size: 16px; margin-bottom: 5px;">个人简历</p>
        <p style="color: #6B7280; font-size: 14px;">生成日期: ${currentDate}</p>
      </div>
      
      <h2 style="color: #4F46E5; border-bottom: 2px solid #4F46E5; padding-bottom: 5px; margin-top: 25px; margin-bottom: 15px; font-size: 18px;">
        个人信息
      </h2>
      <div style="color: #374151; margin-bottom: 20px;">
        <p style="margin-bottom: 8px;"><strong>邮箱:</strong> <EMAIL></p>
        <p style="margin-bottom: 8px;"><strong>电话:</strong> +86 xxx-xxxx-xxxx</p>
        <p style="margin-bottom: 8px;"><strong>地址:</strong> [您的地址]</p>
        <p style="margin-bottom: 8px;"><strong>学生档案:</strong> <span style="color: #4F46E5;">${formData.studentProfile}</span></p>
      </div>
      
      <h2 style="color: #4F46E5; border-bottom: 2px solid #4F46E5; padding-bottom: 5px; margin-top: 25px; margin-bottom: 15px; font-size: 18px;">
        教育背景
      </h2>
      <div style="color: #374151; margin-bottom: 20px;">
        <p style="margin-bottom: 8px;"><strong>[学校名称]</strong></p>
        <p style="margin-bottom: 8px;">[专业] | GPA: X.XX/4.0</p>
        <p style="margin-bottom: 8px;">[入学年份] - [毕业年份]</p>
      </div>
      
      <h2 style="color: #4F46E5; border-bottom: 2px solid #4F46E5; padding-bottom: 5px; margin-top: 25px; margin-bottom: 15px; font-size: 18px;">
        实习与项目经历
      </h2>
      <div style="color: #374151; margin-bottom: 20px;">
        <p style="margin-bottom: 8px;">[请补充您的实习、项目或研究经历]</p>
      </div>
      
      <h2 style="color: #4F46E5; border-bottom: 2px solid #4F46E5; padding-bottom: 5px; margin-top: 25px; margin-bottom: 15px; font-size: 18px;">
        技能特长
      </h2>
      <div style="color: #374151; margin-bottom: 20px;">
        <p style="margin-bottom: 8px;">[请补充您的技能特长]</p>
      </div>
      
      ${formData.additionalInfo ? `
      <h2 style="color: #4F46E5; border-bottom: 2px solid #4F46E5; padding-bottom: 5px; margin-top: 25px; margin-bottom: 15px; font-size: 18px;">
        补充信息
      </h2>
      <div style="color: #374151; margin-bottom: 20px;">
        <p style="line-height: 1.6;">${formData.additionalInfo.replace(/\n/g, '<br>')}</p>
      </div>
      ` : ''}
    `
  }
}

// 保存处理
const handleSave = (data) => {
  console.log('Saving CV:', data)
  // 这里实现保存到后端的逻辑
}

// 导出处理
const handleExport = (data) => {
  console.log('Exporting CV:', data)
  // 这里实现导出功能的逻辑
}
</script> 

<style scoped>
/* 表单标签样式 */
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* 表单输入框样式 */
.form-input :deep(.el-input__wrapper) {
  @apply border-gray-300 rounded-lg shadow-sm;
}

.form-input :deep(.el-input__wrapper:focus-within) {
  @apply !border-[#4F46E5];
  box-shadow: 0 0 0 1px #4F46E5 inset !important;
}

.form-textarea :deep(.el-textarea__inner) {
  @apply border-gray-300 rounded-lg shadow-sm;
}

.form-textarea :deep(.el-textarea__inner:focus) {
  @apply !border-[#4F46E5];
  box-shadow: 0 0 0 1px #4F46E5 inset !important;
}

/* 移除了pro-card相关样式，现在使用直接布局 */



/* Material Icons */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  line-height: 1;
  transition: color 0.2s ease;
}

/* 按钮悬停效果 */
button:hover {
  @apply transform transition-transform duration-200;
}

/* 覆盖 Element Plus 主题色 */
:deep(.el-input) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-textarea) {
  --el-color-primary: #4F46E5 !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .w-96 {
    @apply w-full;
  }
  
  .flex {
    @apply flex-col;
  }
  
  .h-screen {
    @apply min-h-screen;
  }
  
  /* 移动端调整 */
  .w-96 {
    @apply w-full;
  }
  
  .px-6 {
    @apply px-4;
  }
  
  .p-6 {
    @apply p-4;
  }
}
</style> 