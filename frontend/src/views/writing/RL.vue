<template>
  <div class="h-screen bg-white flex overflow-hidden">
    <!-- 左侧表单区域 -->
    <div class="w-96 flex-shrink-0 bg-white border-r border-gray-200 flex flex-col h-full">
      <div class="flex-1 overflow-y-auto p-6">
          <div class="space-y-6">
            <!-- 学生档案 -->
            <div>
              <div class="flex items-center justify-between mb-3">
                <label class="form-label text-red-500">
                  <span class="text-red-500 mr-1">*</span>学生档案
                </label>
                <button class="text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium">
                  + 新建档案
                </button>
              </div>
              <el-input
                v-model="formData.studentProfile"
                placeholder="请选择或搜索学生档案..."
                class="form-input"
                readonly
              >
                <template #suffix>
                  <span class="material-icons-outlined text-gray-400 text-lg">expand_more</span>
                </template>
              </el-input>
            </div>

            <!-- 申请院校 -->
            <div>
              <label class="form-label text-red-500">
                <span class="text-red-500 mr-1">*</span>申请院校
              </label>
              <el-select 
                v-model="formData.targetSchool" 
                placeholder="请选择或搜索申请院校..." 
                class="w-full form-input"
                filterable
              >
                <el-option label="哈佛大学" value="harvard"></el-option>
                <el-option label="斯坦福大学" value="stanford"></el-option>
                <el-option label="麻省理工学院" value="mit"></el-option>
                <el-option label="加州大学伯克利分校" value="berkeley"></el-option>
                <el-option label="牛津大学" value="oxford"></el-option>
                <el-option label="剑桥大学" value="cambridge"></el-option>
              </el-select>
            </div>

            <!-- 申请学位 -->
            <div>
              <label class="form-label text-red-500">
                <span class="text-red-500 mr-1">*</span>申请学位
              </label>
              <div class="flex space-x-3">
                <button 
                  @click="formData.degree = 'bachelor'"
                  :class="[
                    'flex-1 py-2.5 px-4 rounded-lg text-sm font-medium transition-colors duration-200',
                    formData.degree === 'bachelor' 
                      ? 'bg-[#4F46E5] text-white shadow-sm' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-200'
                  ]"
                >
                  本科
                </button>
                <button 
                  @click="formData.degree = 'master'"
                  :class="[
                    'flex-1 py-2.5 px-4 rounded-lg text-sm font-medium transition-colors duration-200',
                    formData.degree === 'master' 
                      ? 'bg-[#4F46E5] text-white shadow-sm' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-200'
                  ]"
                >
                  硕士
                </button>
                <button 
                  @click="formData.degree = 'phd'"
                  :class="[
                    'flex-1 py-2.5 px-4 rounded-lg text-sm font-medium transition-colors duration-200',
                    formData.degree === 'phd' 
                      ? 'bg-[#4F46E5] text-white shadow-sm' 
                      : 'bg-gray-100 text-gray-600 hover:bg-gray-200 border border-gray-200'
                  ]"
                >
                  博士
                </button>
              </div>
            </div>

            <!-- 申请专业 -->
            <div>
              <label class="form-label text-red-500">
                <span class="text-red-500 mr-1">*</span>申请专业
                <span class="material-icons-outlined text-gray-400 text-sm ml-1 cursor-help" title="选择专业信息">help_outline</span>
              </label>
              <el-select 
                v-model="formData.major" 
                placeholder="选择专业" 
                class="w-full form-input"
                filterable
              >
                <el-option label="计算机科学" value="computer_science"></el-option>
                <el-option label="商业管理" value="business_administration"></el-option>
                <el-option label="工程学" value="engineering"></el-option>
                <el-option label="经济学" value="economics"></el-option>
                <el-option label="心理学" value="psychology"></el-option>
                <el-option label="生物学" value="biology"></el-option>
              </el-select>
            </div>

            <!-- 推荐人信息 -->
            <div>
              <label class="form-label text-red-500">
                <span class="text-red-500 mr-1">*</span>推荐人信息
                <button class="ml-2 text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium">
                  上传
                </button>
              </label>
              <el-input
                v-model="formData.recommenderInfo"
                type="textarea"
                :rows="3"
                placeholder="请输入推荐人名字、学校名称、学校职位、联系方式等信息"
                class="form-textarea"
              ></el-input>
            </div>

            <!-- 推荐原因 -->
            <div>
              <label class="form-label text-red-500">
                <span class="text-red-500 mr-1">*</span>推荐原因
                <button class="ml-2 text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium">
                  查看范例
                </button>
                <button class="ml-2 text-[#4F46E5] text-sm hover:text-[#4338CA] transition-colors duration-200 font-medium">
                  上传
                </button>
              </label>
              <el-input
                v-model="formData.recommendationReason"
                type="textarea"
                :rows="4"
                placeholder="请输入推荐人与学生的关系、推荐理由、学生的成就等信息"
                class="form-textarea"
              ></el-input>
            </div>

            <!-- 目标字数 -->
            <div>
              <div class="flex items-center justify-between mb-3">
                <label class="form-label mb-0">目标字数</label>
                <span class="text-sm font-medium text-[#4F46E5]">{{ getWordLimitDisplay(formData.wordLimit) }}</span>
              </div>
              <div class="px-2">
                <el-slider
                  v-model="formData.wordLimit"
                  :marks="wordLimitMarks"
                  :step="1"
                  :min="0"
                  :max="4"
                  :show-tooltip="false"
                  class="custom-slider"
                />
              </div>
            </div>

            <!-- 底部按钮区域 -->
            <div class="pt-6 border-t border-gray-100 mt-6">
              <button 
                @click="handleGenerateRecommendation" 
                :disabled="isGenerating || !isFormValid"
                :class="[
                  'w-full py-3 px-4 rounded-lg font-medium text-white transition-colors duration-200 flex items-center justify-center',
                  isFormValid && !isGenerating
                    ? 'bg-[#4F46E5] hover:bg-[#4338CA] shadow-sm cursor-pointer'
                    : 'bg-gray-400 cursor-not-allowed'
                ]"
              >
                <span v-if="isGenerating">
                  <span class="material-icons-outlined animate-spin mr-2 text-lg">refresh</span>
                  生成中...
                </span>
                <span v-else>
                  <span class="material-icons-outlined mr-2 text-lg">auto_fix_high</span>
                  提交生成
                </span>
              </button>
            </div>
          </div>
        </div>
    </div>

    <!-- 右侧编辑器区域 -->
    <div class="flex-1 bg-white">
      <SharedTextEditor
        v-model="recommendationContent"
        document-type="recommendation"
        placeholder="开始编写推荐信，或点击左侧'提交生成'获取AI建议..."
        @save="handleSave"
        @export="handleExport"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElSlider } from 'element-plus'
import SharedTextEditor from '@/components/writing/SharedTextEditor.vue'

// 表单数据
const formData = reactive({
  studentProfile: '',
  targetSchool: '',
  degree: 'master',
  major: '',
  recommenderInfo: '',
  recommendationReason: '',
  wordLimit: 0 // 滑动器数值：0=不限制, 1=500, 2=1000, 3=1500, 4=2000
})

// 目标字数滑动器标记
const wordLimitMarks = {
  0: '不限制',
  1: '500',
  2: '1000', 
  3: '1500',
  4: '2000'
}

// 推荐信内容
const recommendationContent = ref('')
const isGenerating = ref(false)

// 获取字数限制显示文本
const getWordLimitDisplay = (value) => {
  const displays = ['不限制', '500词', '1000词', '1500词', '2000词']
  return displays[value] || '不限制'
}

// 验证表单是否有效
const isFormValid = computed(() => {
  return formData.studentProfile && 
         formData.targetSchool && 
         formData.degree && 
         formData.major && 
         formData.recommenderInfo && 
         formData.recommendationReason
})

// 生成推荐信初稿
const handleGenerateRecommendation = async () => {
  if (!isFormValid.value) {
    ElMessage.warning('请完成所有必填项目')
    return
  }

  isGenerating.value = true
  try {
    ElMessage.success('正在生成推荐信初稿，请稍候...')
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 生成的推荐信模板
    const generatedRecommendation = generateRecommendationTemplate()
    recommendationContent.value = generatedRecommendation
    
    ElMessage.success('推荐信初稿生成完成，您可以继续编辑')
  } catch (error) {
    ElMessage.error('生成失败，请重试')
    console.error('Generate Recommendation error:', error)
  } finally {
    isGenerating.value = false
  }
}

// 生成推荐信模板
const generateRecommendationTemplate = () => {
  const currentDate = new Date().toLocaleDateString('zh-CN')
  const wordLimitText = getWordLimitDisplay(formData.wordLimit)
  
  return `
    <div style="margin-bottom: 30px; color: #374151;">
      <p style="margin-bottom: 20px; font-weight: 500; color: #4F46E5; font-size: 16px; text-align: center;">
        推荐信
      </p>
      <p style="margin-bottom: 10px;">日期: ${currentDate}</p>
      <p style="margin-bottom: 10px;">申请院校: <strong style="color: #4F46E5;">${formData.targetSchool}</strong></p>
      <p style="margin-bottom: 10px;">申请学位: <strong style="color: #4F46E5;">${formData.degree === 'master' ? '硕士' : formData.degree === 'phd' ? '博士' : '学士'}</strong></p>
      <p style="margin-bottom: 10px;">申请专业: <strong style="color: #4F46E5;">${formData.major}</strong></p>
      <p style="margin-bottom: 20px;">目标字数: ${wordLimitText}</p>
    </div>
    
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      尊敬的招生委员会：
    </p>
    
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      ${formData.recommenderInfo}
    </p>
    
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      ${formData.recommendationReason}
    </p>
    
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      我强烈推荐该学生申请贵校的项目。相信他/她将为贵校的学术社区做出重要贡献，并从贵校提供的机会中获得巨大收益。
    </p>
    
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      如需任何额外信息，请随时与我联系。
    </p>
    
    <p style="margin-bottom: 15px; line-height: 1.6; color: #374151;">
      此致<br/>
      敬礼
    </p>
    
    <div style="margin-top: 30px; color: #374151;">
      <p style="margin-bottom: 5px; font-weight: 500;">推荐人签名：_________________</p>
      <p style="margin-bottom: 5px;">日期：${currentDate}</p>
    </div>
  `
}

// 保存处理
const handleSave = (data) => {
  console.log('Saving Recommendation:', data)
  // 这里实现保存到后端的逻辑
}

// 导出处理
const handleExport = (data) => {
  console.log('Exporting Recommendation:', data)
  // 这里实现导出功能的逻辑
}
</script> 

<style scoped>
/* 表单标签样式 */
.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

/* 表单输入框样式 */
.form-input :deep(.el-input__wrapper) {
  @apply border-gray-300 rounded-lg shadow-sm;
}

.form-input :deep(.el-input__wrapper:focus-within) {
  @apply !border-[#4F46E5];
  box-shadow: 0 0 0 1px #4F46E5 inset !important;
}

.form-textarea :deep(.el-textarea__inner) {
  @apply border-gray-300 rounded-lg shadow-sm;
}

.form-textarea :deep(.el-textarea__inner:focus) {
  @apply !border-[#4F46E5];
  box-shadow: 0 0 0 1px #4F46E5 inset !important;
}

/* 选择框样式 */
.form-input :deep(.el-select) {
  @apply w-full;
}

.form-input :deep(.el-select__wrapper) {
  @apply border-gray-300 rounded-lg shadow-sm;
}

.form-input :deep(.el-select__wrapper:focus-within) {
  @apply !border-[#4F46E5];
  box-shadow: 0 0 0 1px #4F46E5 inset !important;
}

/* 移除了pro-card相关样式，现在使用直接布局 */



/* 单选按钮样式 */
input[type="radio"] {
  @apply w-4 h-4;
  accent-color: #4F46E5;
}

/* Material Icons */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  line-height: 1;
  transition: color 0.2s ease;
}

/* 按钮悬停效果 */
button:hover {
  @apply transform transition-transform duration-200;
}

/* 覆盖 Element Plus 主题色 */
:deep(.el-input) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-textarea) {
  --el-color-primary: #4F46E5 !important;
}

:deep(.el-select) {
  --el-color-primary: #4F46E5 !important;
}

/* Element Plus 下拉选择框紫色主题 */
:deep(.el-select-dropdown__item.is-hovering) {
  background-color: rgba(79, 70, 229, 0.1) !important;
  color: #4F46E5 !important;
}

:deep(.el-select-dropdown__item.is-selected) {
  color: #4F46E5 !important;
  font-weight: 500 !important;
}

/* 滑动器样式 */
.custom-slider {
  margin: 15px 0 35px 0;
  height: 50px;
  position: relative;
}

.custom-slider :deep(.el-slider__runway) {
  background-color: #E5E7EB;
  height: 6px;
  border-radius: 3px;
  cursor: pointer;
}

.custom-slider :deep(.el-slider__bar) {
  background-color: #4F46E5;
  height: 6px;
  border-radius: 3px;
}

.custom-slider :deep(.el-slider__button) {
  background-color: #4F46E5;
  border: 2px solid #FFFFFF;
  width: 20px;
  height: 20px;
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.3);
  cursor: grab;
  transition: all 0.2s ease;
  position: relative;
  z-index: 10;
}

.custom-slider :deep(.el-slider__button:hover) {
  background-color: #4338CA;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.4);
}

.custom-slider :deep(.el-slider__button:active) {
  cursor: grabbing;
  transform: scale(0.95);
}

.custom-slider :deep(.el-slider__button-wrapper) {
  cursor: pointer;
  z-index: 10;
  position: relative;
}

.custom-slider :deep(.el-slider__marks) {
  top: 20px;
  position: absolute;
  width: 100%;
}

.custom-slider :deep(.el-slider__marks-text) {
  font-size: 12px;
  color: #6B7280;
  white-space: nowrap;
  cursor: pointer;
  user-select: none;
  position: absolute;
  transform: translateX(-50%);
  line-height: 1;
  margin-top: 5px;
}

.custom-slider :deep(.el-slider__marks .el-slider__marks-text:hover) {
  color: #4F46E5;
  font-weight: 500;
}

/* 确保滑动器可以正常交互 */
.custom-slider :deep(.el-slider) {
  pointer-events: all;
  height: 20px;
  position: relative;
}

.custom-slider :deep(.el-slider__runway) {
  margin: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .w-96 {
    @apply w-full;
  }
  
  .flex {
    @apply flex-col;
  }
  
  .h-screen {
    @apply min-h-screen;
  }
  
  /* 移动端调整 */
  .w-96 {
    @apply w-full;
  }
  
  .px-6 {
    @apply px-4;
  }
  
  .p-6 {
    @apply p-4;
  }
}
</style> 