<template>
  <div class="min-h-screen bg-gray-50 max-w-7xl mx-auto">
    <!-- 欢迎信息卡片 -->
    <div class="bg-gradient-to-r from-primary to-primary-light rounded-xl shadow-lg p-6 mb-6 text-white relative overflow-hidden">
      <!-- 装饰性背景图案 -->
      <div class="absolute top-0 right-0 w-32 h-32 bg-white bg-opacity-10 rounded-full -translate-y-8 translate-x-8"></div>
      <div class="absolute bottom-0 left-0 w-24 h-24 bg-white bg-opacity-5 rounded-full translate-y-6 -translate-x-6"></div>
      
      <div class="relative z-10">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <!-- 用户头像 -->
            <div class="w-12 h-12 rounded-full bg-white bg-opacity-20 flex items-center justify-center backdrop-blur-sm">
              <span class="text-lg font-semibold text-white">{{ displayName.charAt(0).toUpperCase() }}</span>
            </div>
            
            <div>
              <h1 class="text-xl font-semibold text-white">欢迎回来！</h1>
              <p class="text-white text-opacity-90 text-lg font-medium">{{ displayName }}</p>
            </div>
          </div>
          
          <!-- 右侧信息 -->
          <div class="text-right">
            <div class="flex items-center space-x-2 mb-1">
              <span class="material-icons-outlined text-white text-opacity-80 text-sm">email</span>
              <p class="text-white text-opacity-80 text-sm">{{ authStore.user?.email || userStore.userInfo.email }}</p>
            </div>
            <div class="flex items-center space-x-2">
              <span class="material-icons-outlined text-white text-opacity-80 text-sm">
                {{ (authStore.user?.role || userStore.userInfo.role) === 'admin' ? 'admin_panel_settings' : 'person' }}
              </span>
              <p class="text-white text-opacity-80 text-sm">
                {{ (authStore.user?.role || userStore.userInfo.role) === 'admin' ? '管理员' : '普通用户' }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 左侧：近期客户 -->
      <div class="space-y-6">
        <div class="bg-white rounded-xl shadow-sm p-6">
          <div class="flex justify-between items-center mb-4">
            <div>
              <h2 class="text-lg font-semibold text-gray-900">近期客户</h2>
              <p class="text-sm text-gray-500">近期服务的客户</p>
            </div>
            <router-link
              to="/clients"
              class="text-sm text-primary hover:text-primary-dark flex items-center"
            >
              查看全部
              <span class="material-icons-outlined text-sm ml-1">arrow_forward</span>
            </router-link>
          </div>

          <div class="space-y-4">
            <!-- 加载中状态 -->
            <div v-if="isLoading" class="space-y-3">
              <div v-for="i in 3" :key="i" class="flex items-center justify-between p-3 animate-pulse">
                <div class="flex items-center space-x-3">
                  <div class="w-8 h-8 rounded-full bg-gray-200"></div>
                  <div>
                    <div class="h-4 bg-gray-200 rounded w-24 mb-1"></div>
                    <div class="h-3 bg-gray-200 rounded w-16"></div>
                  </div>
                </div>
                <div class="h-3 bg-gray-200 rounded w-16"></div>
              </div>
            </div>

            <!-- 无数据状态 -->
            <div v-else-if="recentClients.length === 0" class="text-center py-8">
              <p class="text-gray-500">暂无近期客户</p>
            </div>

            <!-- 有数据状态 -->
            <div
              v-else
              v-for="client in recentClients"
              :key="client.id_hashed"
              class="flex items-center justify-between p-3 hover:bg-gray-50 rounded-lg transition-colors cursor-pointer"
              @click="handleViewClient(client.id_hashed)"
            >
              <div class="flex items-center space-x-3">
                <div class="w-8 h-8 rounded-full bg-primary bg-opacity-10 flex items-center justify-center text-primary">
                  {{ client.name?.charAt(0)?.toUpperCase() || '?' }}
                </div>
                <div>
                  <div class="font-medium text-sm text-gray-900">{{ client.name }}</div>
                  <div class="text-xs text-gray-500">{{ client.location || '未知地区' }}</div>
                </div>
              </div>
              <div class="text-xs text-gray-500">
                {{ formatDate(client.updated_at || client.updatedAt) }}
              </div>
            </div>
          </div>
        </div>

        <div class="bg-white rounded-xl shadow-sm p-6">
          <h2 class="text-lg font-semibold text-gray-900 mb-4">近期作品</h2>
          <p class="text-sm text-gray-500 mb-4">最近完成的作品列表</p>

          <!-- 加载中状态 -->
          <div v-if="isLoading" class="space-y-3">
            <div class="h-16 bg-gray-100 rounded animate-pulse"></div>
            <div class="h-16 bg-gray-100 rounded animate-pulse"></div>
          </div>

          <!-- 无数据状态 -->
          <div v-else-if="recentWorks.length === 0" class="text-center py-8">
            <p class="text-gray-500 mb-4">暂无近期作品</p>
            <div class="space-x-4">
              <router-link to="/dashboard" class="text-primary hover:text-primary-dark text-sm">查看全部</router-link>
              <router-link to="/write/recommendation" class="text-primary hover:text-primary-dark text-sm">创建文档</router-link>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间：核心能力 -->
      <div class="bg-white rounded-xl shadow-sm p-6">
        <h2 class="text-lg font-semibold text-gray-900 mb-2">核心功能</h2>
        <p class="text-sm text-gray-500 mb-6">多种功能助力高效服务客户</p>

        <div class="space-y-4" v-memo="[1]"> <!-- 使用v-memo优化静态内容渲染 -->
          <!-- 智能建档 -->
          <div class="p-4 rounded-lg border border-gray-100 hover:border-primary transition-colors group cursor-pointer">
            <div class="flex items-start space-x-4">
              <div class="w-10 h-10 rounded-lg bg-primary bg-opacity-10 flex items-center justify-center flex-shrink-0">
                <UserIcon class="w-6 h-6 text-primary" />
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900">智能建档 </h3>
                <p class="text-sm text-gray-500 mt-1">上传学生信息意愿一分钟快速建档 </p>
                <router-link to="/clients" class="text-primary hover:text-primary-dark text-sm mt-2 inline-block group-hover:underline">开始建档 ›</router-link>
              </div>
            </div>
          </div>

          <!-- 选校助手 -->
          <div class="p-4 rounded-lg border border-gray-100 hover:border-primary transition-colors group cursor-pointer">
            <div class="flex items-start space-x-4">
              <div class="w-10 h-10 rounded-lg bg-yellow-100 flex items-center justify-center flex-shrink-0">
                <AcademicCapIcon class="w-6 h-6 text-yellow-600" />
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900">选校助手</h3>
                <p class="text-sm text-gray-500 mt-1">基于上万录取数据分析，精准推荐冲刺/匹配/保底院校清单</p>
                <router-link to="/school-assistant" class="text-primary hover:text-primary-dark text-sm mt-2 inline-block group-hover:underline">开始选校 ›</router-link>
              </div>
            </div>
          </div>

          <!-- 写推荐信 -->
          <div class="p-4 rounded-lg border border-gray-100 hover:border-primary transition-colors group cursor-pointer">
            <div class="flex items-start space-x-4">
              <div class="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center flex-shrink-0">
                <DocumentTextIcon class="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900">写推荐信</h3>
                <p class="text-sm text-gray-500 mt-1">可自由控制大纲的推荐信生成大模型，确保你的推荐信不会千篇一律</p>
                <router-link to="/write/recommendation" class="text-primary hover:text-primary-dark text-sm mt-2 inline-block group-hover:underline">生成推荐信 ›</router-link>
              </div>
            </div>
          </div>

          <!-- 写简历 -->
          <div class="p-4 rounded-lg border border-gray-100 hover:border-primary transition-colors group cursor-pointer">
            <div class="flex items-start space-x-4">
              <div class="w-10 h-10 rounded-lg bg-purple-100 flex items-center justify-center flex-shrink-0">
                <DocumentIcon class="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900">写简历</h3>
                <p class="text-sm text-gray-500 mt-1">可自由控制大纲的简历生成大模型，确保你的简历不会千篇一律</p>
                <router-link to="/write/cv" class="text-primary hover:text-primary-dark text-sm mt-2 inline-block group-hover:underline">生成简历 ›</router-link>
              </div>
            </div>
          </div>

          <!-- 写PS -->
          <div class="p-4 rounded-lg border border-gray-100 hover:border-primary transition-colors group cursor-pointer">
            <div class="flex items-start space-x-4">
              <div class="w-10 h-10 rounded-lg bg-green-100 flex items-center justify-center flex-shrink-0">
                <DocumentTextIcon class="w-6 h-6 text-green-600" />
              </div>
              <div>
                <h3 class="text-base font-medium text-gray-900">写PS</h3>
                <p class="text-sm text-gray-500 mt-1">可自由控制大纲的PS生成大模型，确保你的个人陈述不会千篇一律</p>
                <router-link to="/write/ps" class="text-primary hover:text-primary-dark text-sm mt-2 inline-block group-hover:underline">生成个人陈述 ›</router-link>
              </div>
            </div>
          </div>

          <!-- 其他功能 -->
          <div class="space-y-4">
            <div class="p-4 rounded-lg border border-gray-100 hover:border-primary transition-colors group cursor-pointer">
              <div class="flex items-start space-x-4">
                <div class="w-10 h-10 rounded-lg bg-red-100 flex items-center justify-center flex-shrink-0">
                  <PencilSquareIcon class="w-6 h-6 text-red-600" />
                </div>
                <div>
                  <h3 class="text-base font-medium text-gray-900">移除AI痕迹</h3>
                  <p class="text-sm text-gray-500 mt-1">TunshuEdu自主研发的拟人化转写大模型，让AI查重率瞬间跳水</p>
                  <router-link to="/ai-reducer" class="text-primary hover:text-primary-dark text-sm mt-2 inline-block group-hover:underline">使用清除工具 ›</router-link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：更新动态 -->
      <div>
        <!-- 更新动态 -->
        <div class="bg-white rounded-xl shadow-sm p-4">
          <div class="flex justify-between items-center mb-3">
            <h2 class="text-base font-semibold text-gray-900">更新</h2>
            <span class="text-xs text-gray-500">What's new</span>
          </div>
          <div class="space-y-3" v-memo="[updates.length]"> <!-- 只有当updates数组长度变化时才重新渲染 -->
            <div v-for="update in updates" :key="update.id" class="border-l-2 border-primary pl-3 py-2">
              <div class="flex items-center space-x-2 text-xs text-gray-500">
                <span>📅</span>
                <span>{{ update.date }}</span>
              </div>
              <h3 class="text-sm font-medium text-gray-900 mt-1">{{ update.title }}</h3>
              <p class="text-xs text-gray-500 mt-1 leading-relaxed">{{ update.content }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useAuthStore } from '@/stores/auth'
import { useRouter } from 'vue-router'
import { getClientList } from '@/api/client'
import {
  DocumentTextIcon,
  DocumentIcon,
  PencilSquareIcon,
  UserIcon,
  AcademicCapIcon
} from '@heroicons/vue/24/outline'
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn' // 导入中文语言包

// 设置 dayjs 语言为中文
dayjs.locale('zh-cn')

const userStore = useUserStore()
const authStore = useAuthStore()
const router = useRouter()

// 用户显示名称
const displayName = computed(() => {
  // 优先使用authStore中的最新信息，因为账户设置页面会直接更新authStore
  return authStore.user?.nickname ||
         authStore.user?.username ||
         userStore.userInfo.nickname ||
         userStore.userInfo.username ||
         '用户'
})

// 近期客户数据
const recentClients = ref([])
// 近期作品数据（目前为空，后续可从API获取）
const recentWorks = ref([
  // 示例数据，如果有API可以替换为实际数据
  // {
  //   id: 1,
  //   title: '推荐信 - 张同学',
  //   type: 'recommendation',
  //   date: '2025-05-28'
  // }
])
const isLoading = ref(true)

const updates = ref([
  {
    id: 1,
    title: 'TunshuEdu v1.0内测版发布',
    content: '我们很高兴地宣布TunshuEdu v1.0内测版正式上线！本次测试版包含完整的留学申请AI辅助功能，包括智能定校推荐、文书生成与优化、申请材料管理等核心模块。测试期间，所有功能均可免费使用，欢迎提出宝贵意见以帮助我们进一步优化产品体验。',
    date: '2025-08-01'
  }
])

// 添加日期格式化函数
const formatDate = (date) => {
  if (!date) return '-'
  // 直接格式化日期，不需要时区转换
  return dayjs(date).format('YYYY-MM-DD')
}

// 获取近期客户列表
const fetchRecentClients = async () => {
  try {
    // 设置超时时间，避免请求长时间挂起
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000) // 减少到5秒超时

    // 添加缓存参数，避免浏览器缓存
    const response = await getClientList({
      limit: 5, // 只获取最近5个客户
      sort: 'updated_at:desc', // 按修改时间倒序排序
      signal: controller.signal,
      _t: new Date().getTime() // 添加时间戳防止缓存
    })

    // 清除超时计时器
    clearTimeout(timeoutId)

    // 处理响应数据
    let clients = []
    if (Array.isArray(response)) {
      clients = response
    } else if (response?.items && Array.isArray(response.items)) {
      clients = response.items
    } else if (response?.data && Array.isArray(response.data)) {
      // 处理可能的另一种API响应格式
      clients = response.data
    } else {
      // 如果响应格式不符合预期，设置为空数组
      console.warn('获取客户列表响应格式不符合预期:', response)
    }
    
    // 确保按照修改时间排序，最近的在前面
    clients.sort((a, b) => {
      const dateA = new Date(a.updated_at || a.updatedAt || 0)
      const dateB = new Date(b.updated_at || b.updatedAt || 0)
      return dateB - dateA
    })
    
    // 限制最多显示5个
    recentClients.value = clients.slice(0, 5)
  } catch (error) {
    // 区分超时错误和其他错误
    if (error.name === 'AbortError') {
      console.warn('获取客户列表请求超时')
    } else {
      console.error('获取近期客户失败:', error)
    }

    // 发生错误时，确保设置为空数组而不是保持之前的状态
    recentClients.value = []
    // 可以在这里添加错误通知，但为了用户体验，在Dashboard页面不显示错误提示
  }
}

// 查看客户详情
const handleViewClient = (clientId) => {
  router.push(`/clients/${clientId}`)
}

// 在组件挂载时获取数据 - 使用非阻塞方式
onMounted(() => {
  // 立即设置一个延时器来结束加载状态，确保骨架屏至少显示一小段时间
  const minLoadingTimer = setTimeout(() => {
    if (isLoading.value) {
      isLoading.value = false
    }
  }, 800)

  // 使用 Promise.allSettled 并行加载数据，但不使用 await 阻塞渲染
  Promise.allSettled([
    userStore.fetchUserInfo(true), // 强制刷新用户信息，确保获取最新数据
    fetchRecentClients()
  ])
  .then(results => {
    // 检查每个请求的状态
    results.forEach((result, index) => {
      if (result.status === 'rejected') {
        const apis = ['用户信息', '客户列表']
        console.warn(`${apis[index]}加载失败:`, result.reason)
      }
    })
  })
  .catch(error => {
    console.error('加载数据时出错:', error)
  })
  .finally(() => {
    // 清除最小加载时间定时器
    clearTimeout(minLoadingTimer)
    // 无论成功失败，都结束加载状态
    isLoading.value = false
  })
})
</script>

<style scoped>
/* Material Icons 支持 */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.grid {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

@media (min-width: 1024px) {
  .grid {
    grid-template-columns: 1fr 1.5fr 1fr;
  }
}

/* 添加渐变动画 */
.bg-gradient-to-br {
  background-size: 200% 200%;
  animation: gradient 6s ease infinite;
}

@keyframes gradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 添加卡片悬停效果 */
.rounded-lg {
  transition: all 0.3s ease;
}

.rounded-lg:hover {
  transform: translateY(0px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px 0px rgba(0, 0, 0, 0.05);
}

/* 添加内容加载动画 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(0px); }
  to { opacity: 1; transform: translateY(0); }
}

.bg-white {
  animation: fadeIn 0.3s ease-out forwards;
}
</style>