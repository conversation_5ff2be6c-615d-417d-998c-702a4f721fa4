<template>
  <div class="ai-reducer-page max-w-7xl mx-auto">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">AI率降低</h1>
        <p class="mt-1 text-sm text-gray-500">
          通过智能改写，降低文书中的AI痕迹，提高内容的独特性和个人风格
        </p>
      </div>
    </div>

    <!-- 功能开发中提示 -->
    <div class="bg-white rounded-xl shadow-sm p-8">
      <div class="text-center">
        <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-purple-50 flex items-center justify-center">
          <span class="material-icons-outlined text-purple-500 text-3xl">construction</span>
        </div>
        <h2 class="text-2xl font-medium text-gray-700 mb-3">功能开发中</h2>
        <p class="text-gray-500 mb-8 max-w-lg mx-auto">
          AI率降低功能正在紧张开发中，我们将很快提供这一创新功能，敬请期待...
        </p>
        
        <div class="flex justify-center">
          <router-link to="/dashboard" class="px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors">
            返回仪表盘
          </router-link>
        </div>
      </div>
    </div>
    
    <!-- 预览功能介绍 -->
    <div class="mt-8 bg-white rounded-xl shadow-sm p-6">
      <h3 class="text-lg font-medium text-gray-800 mb-4">即将推出的功能</h3>
      
      <div class="space-y-4">
        <div class="flex items-start">
          <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3 mt-1">
            <span class="material-icons-outlined text-purple-600 text-sm">auto_awesome</span>
          </div>
          <div>
            <h4 class="font-medium text-gray-700">智能改写引擎</h4>
            <p class="text-gray-500 text-sm">保留原文意思的同时，使用更自然的人类写作风格重写文本</p>
          </div>
        </div>
        
        <div class="flex items-start">
          <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3 mt-1">
            <span class="material-icons-outlined text-purple-600 text-sm">tune</span>
          </div>
          <div>
            <h4 class="font-medium text-gray-700">风格调整选项</h4>
            <p class="text-gray-500 text-sm">提供多种写作风格选项，根据申请需求调整文书语言风格</p>
          </div>
        </div>
        
        <div class="flex items-start">
          <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3 mt-1">
            <span class="material-icons-outlined text-purple-600 text-sm">compare</span>
          </div>
          <div>
            <h4 class="font-medium text-gray-700">前后对比分析</h4>
            <p class="text-gray-500 text-sm">直观展示改写前后的AI率变化，帮助评估改写效果</p>
          </div>
        </div>
        
        <div class="flex items-start">
          <div class="w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3 mt-1">
            <span class="material-icons-outlined text-purple-600 text-sm">history</span>
          </div>
          <div>
            <h4 class="font-medium text-gray-700">改写历史保存</h4>
            <p class="text-gray-500 text-sm">保存所有改写版本，方便随时查看和比较不同版本</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// AI率降低页面逻辑
</script>

<style scoped>
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: 'liga';
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}
</style> 