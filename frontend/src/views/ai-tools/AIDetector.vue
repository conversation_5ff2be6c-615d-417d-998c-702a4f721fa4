<template>
  <div class="ai-detector-page max-w-7xl mx-auto">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center mb-6">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">AI率检测</h1>
        <p class="mt-1 text-sm text-gray-500">
          检测您的文书中AI生成内容的比例，帮助您评估文书的原创性
        </p>
      </div>
    </div>

    <!-- 功能开发中提示 -->
    <div class="bg-white rounded-xl shadow-sm p-8">
      <div class="text-center">
        <div class="w-16 h-16 mx-auto mb-4 rounded-full bg-primary bg-opacity-10 flex items-center justify-center">
          <span class="material-icons-outlined text-primary text-3xl">build</span>
        </div>
        <h2 class="text-2xl font-medium text-gray-700 mb-3">功能开发中</h2>
        <p class="text-gray-500 mb-8 max-w-lg mx-auto">
          AI率检测功能正在紧张开发中，我们将很快提供这一功能，敬请期待...
        </p>
        
        <div class="flex justify-center">
          <router-link to="/dashboard" class="px-4 py-2 bg-primary hover:bg-primary-dark text-white rounded-lg transition-colors">
            返回仪表盘
          </router-link>
        </div>
      </div>
    </div>
    
    <!-- 预览功能介绍 -->
    <div class="mt-8 bg-white rounded-xl shadow-sm p-6">
      <h3 class="text-lg font-medium text-gray-800 mb-4">即将推出的功能</h3>
      
      <div class="space-y-4">
        <div class="flex items-start">
          <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3 mt-1">
            <span class="material-icons-outlined text-green-600 text-sm">check</span>
          </div>
          <div>
            <h4 class="font-medium text-gray-700">多模型AI检测</h4>
            <p class="text-gray-500 text-sm">同时使用多种AI检测模型，提供更准确的检测结果</p>
          </div>
        </div>
        
        <div class="flex items-start">
          <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3 mt-1">
            <span class="material-icons-outlined text-green-600 text-sm">check</span>
          </div>
          <div>
            <h4 class="font-medium text-gray-700">段落级别分析</h4>
            <p class="text-gray-500 text-sm">对文书进行段落级别的AI内容分析，精确定位需要修改的部分</p>
          </div>
        </div>
        
        <div class="flex items-start">
          <div class="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3 mt-1">
            <span class="material-icons-outlined text-green-600 text-sm">check</span>
          </div>
          <div>
            <h4 class="font-medium text-gray-700">历史记录与对比</h4>
            <p class="text-gray-500 text-sm">保存检测历史记录，对比文书修改前后的AI率变化</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// AI率检测页面逻辑
</script>

<style scoped>
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: 'liga';
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}
</style> 