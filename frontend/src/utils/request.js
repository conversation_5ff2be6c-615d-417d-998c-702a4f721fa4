/**
 * request.js - HTTP请求工具
 *
 * 本模块封装了基于axios的HTTP请求工具，提供了以下功能：
 * 1. 创建统一配置的axios实例
 * 2. 请求拦截器 - 自动添加认证令牌
 * 3. 响应拦截器 - 统一数据提取和错误处理
 * 4. 令牌刷新机制 - 处理令牌过期情况
 * 5. 请求重试队列 - 在令牌刷新后重试失败的请求
 */

import axios from 'axios'
import { ElMessage } from 'element-plus'
import { getToken, getRefreshToken, clearAuth } from './auth'
import { refreshToken } from '@/api/auth'

// 是否为开发环境
const isDev = process.env.NODE_ENV !== 'production'

// 开发环境下输出API基础URL，用于调试
if (isDev) {
  console.log('API Base URL:', import.meta.env.VITE_API_URL)
}

/**
 * 创建axios实例
 *
 * 配置说明：
 * - baseURL: API的基础URL，从环境变量获取，默认为本地开发服务器
 * - timeout: 请求超时时间，单位毫秒
 * - headers: 默认请求头
 * - withCredentials: 跨域请求是否发送Cookie
 */
const request = axios.create({
  baseURL: import.meta.env.VITE_API_URL || '',
  timeout: 8000, // 减少超时时间到8秒，避免长时间等待
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  },
  withCredentials: true
})

/**
 * 令牌刷新机制相关变量和函数
 *
 * 当遇到401错误（令牌过期）时，我们需要刷新令牌并重试失败的请求。
 * 为了避免多个请求同时触发令牌刷新，我们使用以下机制：
 * 1. 使用isRefreshing标志跟踪是否正在刷新令牌
 * 2. 将等待令牌刷新的请求添加到队列中
 * 3. 令牌刷新成功后，重试队列中的所有请求
 */

// 是否正在刷新token的标志
let isRefreshing = false

// 存储等待token刷新的请求队列
let refreshSubscribers = []

// 最大重试次数
const MAX_RETRY_COUNT = 3

/**
 * 添加请求到等待队列
 *
 * @param {Function} cb - 回调函数，接收新token并重试请求
 */
function subscribeTokenRefresh(cb) {
  refreshSubscribers.push(cb)
}

/**
 * 执行等待队列中的所有请求
 *
 * @param {string} token - 新的访问令牌
 */
function onTokenRefreshed(token) {
  refreshSubscribers.map(cb => cb(token))
  // 清空队列
  refreshSubscribers = []
}

/**
 * 请求拦截器
 *
 * 功能：
 * 1. 自动添加认证令牌到请求头
 * 2. 记录请求信息（仅在开发环境）
 * 3. 添加请求重试计数
 */
request.interceptors.request.use(
  config => {
    // 从auth工具获取token，而不是直接从localStorage
    const token = getToken()

    // 如果存在token，则在请求头中添加Authorization
    if (token) {
      // 统一添加token
      config.headers['Authorization'] = `Bearer ${token}`

      if (isDev) {
        console.log('[请求拦截器] 已添加认证头:', token.substring(0, 10) + '...')
      }
    }

    // 初始化请求重试计数
    if (!config.retryCount) {
      config.retryCount = 0
    }

    // 仅在开发环境输出调试信息
    if (isDev) {
      console.log(`[请求拦截器] ${config.method.toUpperCase()} ${config.url}`)
    }

    return config
  },
  error => {
    // 请求配置错误处理
    if (isDev) {
      console.error('[请求拦截器] 错误:', error)
    }
    return Promise.reject(error)
  }
)

/**
 * 响应拦截器
 *
 * 功能：
 * 1. 自动提取响应数据（response.data）
 * 2. 处理HTTP错误，包括认证错误（401）
 * 3. 实现令牌刷新机制
 * 4. 请求重试机制
 */
request.interceptors.response.use(
  // 成功响应处理
  response => {
    // 仅在开发环境输出调试信息
    if (isDev) {
      console.log(`[响应拦截器] 成功: ${response.config.method.toUpperCase()} ${response.config.url}`)
    }
    // 直接返回响应数据，简化后续处理
    return response.data
  },

  // 错误响应处理
  async error => {
    const { config, response } = error

    // 仅在开发环境输出调试信息
    if (isDev) {
      // 记录请求失败信息
      if (config) {
        console.error(`[响应拦截器] 请求失败: ${config.method.toUpperCase()} ${config.url}`)
      }
      console.error('[响应拦截器] 错误状态:', response?.status)
      console.error('[响应拦截器] 错误详情:', response?.data || error.message)
    }

    // 如果请求已被取消，直接返回错误
    if (axios.isCancel(error)) {
      return Promise.reject(error)
    }

    // 如果没有配置对象，无法重试请求
    if (!config) {
      ElMessage.error('网络请求失败')
      return Promise.reject(error)
    }

    // 检查是否可以重试请求
    if (config.retryCount < MAX_RETRY_COUNT && !config._isRetry && response?.status >= 500) {
      config.retryCount += 1
      config._isRetry = true

      if (isDev) {
        console.log(`[响应拦截器] 服务器错误，第${config.retryCount}次重试请求: ${config.url}`)
      }

      // 延迟重试，避免立即重试导致服务器过载
      return new Promise(resolve => {
        setTimeout(() => {
          resolve(request(config))
        }, 1000 * config.retryCount) // 递增延迟
      })
    }

    // 对于认证相关的错误（400, 401, 422等），不显示全局错误消息，让组件自己处理错误显示
    const isAuthError = config.url?.includes('/auth/') && [400, 401, 422].includes(response?.status)
    
    // 如果不是 401 错误，或者是认证相关的401错误，直接返回错误（不显示ElMessage）
    if (response?.status !== 401 || isAuthError) {
      if (!isAuthError) {
        // 根据不同的错误状态码显示不同的错误消息
        let errorMessage = '请求失败'

        switch (response?.status) {
          case 403:
            errorMessage = '没有权限执行此操作'
            break
          case 404:
            errorMessage = '请求的资源不存在'
            break
          case 500:
            errorMessage = '服务器内部错误'
            break
          default:
            errorMessage = response?.data?.detail || '请求失败'
        }

        ElMessage.error(errorMessage)
      }
      
      return Promise.reject(error)
    }

    // 处理401错误（令牌过期）
    if (isDev) {
      console.log('[响应拦截器] 收到401错误，尝试刷新token...')
    }

    // 避免多个请求同时刷新令牌
    if (isRefreshing) {
      if (isDev) {
        console.log('[响应拦截器] Token刷新中，将请求加入队列')
      }

      try {
        // 将请求添加到等待队列
        return new Promise(resolve => {
          subscribeTokenRefresh(token => {
            if (isDev) {
              console.log('[响应拦截器] 使用新token重试请求')
            }
            config.headers.Authorization = `Bearer ${token}`
            // 重置重试标志
            config._isRetry = false
            resolve(request(config))
          })
        })
      } catch (retryError) {
        if (isDev) {
          console.error('[响应拦截器] 队列重试失败:', retryError)
        }
        return Promise.reject(retryError)
      }
    }

    // 开始刷新token流程
    isRefreshing = true

    // 使用auth工具获取刷新令牌
    const refresh_token = getRefreshToken()

    if (!refresh_token) {
      if (isDev) {
        console.warn('[响应拦截器] 未找到refresh token，执行登出')
      }

      // 清除认证信息
      clearAuth()
      ElMessage.error('登录已过期，请重新登录')

      // 重定向到登录页
      window.location.href = '/login'
      return Promise.reject(error)
    }

    try {
      if (isDev) {
        console.log('[响应拦截器] 开始刷新token')
      }

      // 调用刷新令牌API
      const res = await refreshToken(refresh_token)
      const { access_token } = res

      if (!access_token) {
        throw new Error('token刷新失败，未获取到新token')
      }

      if (isDev) {
        console.log('[响应拦截器] token刷新成功')
      }

      // 执行等待队列中的请求
      onTokenRefreshed(access_token)

      // 重试当前请求
      if (isDev) {
        console.log('[响应拦截器] 重试当前请求:', config.url)
      }

      config.headers.Authorization = `Bearer ${access_token}`
      // 重置重试标志
      config._isRetry = false
      return request(config)
    } catch (refreshError) {
      // 刷新token失败，清除认证信息并跳转到登录页
      if (isDev) {
        console.error('[响应拦截器] 刷新token失败，执行登出:', refreshError)
      }

      // 清除认证信息
      clearAuth()
      ElMessage.error('登录已过期，请重新登录')

      // 重定向到登录页
      window.location.href = '/login'
      return Promise.reject(refreshError)
    } finally {
      // 无论成功失败，都重置刷新状态
      isRefreshing = false
    }
  }
)

export default request