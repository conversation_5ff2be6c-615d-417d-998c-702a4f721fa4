<template>
  <div class="pro-card h-full flex flex-col">
    <!-- 编辑器工具栏 -->
    <div class="pro-card-header">
      <div class="flex items-center space-x-2">
        <el-button 
          @click="handleSave" 
          type="primary" 
          :loading="isSaving"
          class="primary-btn transition-standard hover-scale"
        >
          保存
        </el-button>
        <el-dropdown @command="handleExport" class="transition-standard">
          <el-button class="secondary-btn">
            导出
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="pdf">导出为PDF</el-dropdown-item>
              <el-dropdown-item command="docx">导出为Word</el-dropdown-item>
              <el-dropdown-item command="txt">导出为文本</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
        <el-button 
          @click="handleShare" 
          class="secondary-btn transition-standard hover-scale"
        >
          分享
        </el-button>
      </div>
      <div class="flex items-center space-x-4 text-sm text-gray-500">
        <span class="text-gray-600">字数: <span class="font-medium text-gray-800">{{ wordCount }}</span></span>
        <span v-if="lastSaved" class="text-gray-600">上次保存: <span class="font-medium text-gray-800">{{ lastSaved }}</span></span>
      </div>
    </div>

    <!-- 编辑器区域 -->
    <div class="pro-card-body flex-1 flex flex-col">
      <div ref="editorContainer" class="flex-1 w-full max-w-full overflow-hidden"></div>
    </div>

    <!-- 分享对话框 -->
    <el-dialog 
      v-model="shareDialogVisible" 
      title="分享文档" 
      width="400px"
      class="custom-dialog"
    >
      <div class="space-y-6">
        <div>
          <label class="form-label">邀请他人编辑</label>
          <el-input
            v-model="shareEmail"
            placeholder="输入邮箱地址"
            @keyup.enter="handleInvite"
            class="form-input"
          >
            <template #append>
              <el-button 
                @click="handleInvite" 
                type="primary"
                class="primary-btn"
              >
                邀请
              </el-button>
            </template>
          </el-input>
        </div>
        <div>
          <label class="form-label">分享链接</label>
          <el-input 
            v-model="shareLink" 
            readonly
            class="form-input"
          >
            <template #append>
              <el-button 
                @click="handleCopyLink"
                class="secondary-btn"
              >
                复制
              </el-button>
            </template>
          </el-input>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import Quill from 'quill'
import 'quill/dist/quill.snow.css'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  documentType: {
    type: String,
    required: true // 'cv', 'ps', 'recommendation'
  },
  placeholder: {
    type: String,
    default: '开始写作...'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'save', 'export'])

// 响应式数据
const editorContainer = ref(null)
const quillEditor = ref(null)
const isSaving = ref(false)
const shareDialogVisible = ref(false)
const shareEmail = ref('')
const shareLink = ref('https://example.com/share/doc123')
const lastSaved = ref('')

// 计算字数
const wordCount = computed(() => {
  if (!quillEditor.value) return 0
  const text = quillEditor.value.getText()
  return text.trim().split(/\s+/).filter(word => word.length > 0).length
})

// 编辑器配置
const editorOptions = {
  theme: 'snow',
  placeholder: props.placeholder,
  modules: {
    toolbar: [
      [{ 'header': [1, 2, 3, false] }],
      ['bold', 'italic', 'underline', 'strike'],
      [{ 'color': [] }, { 'background': [] }],
      [{ 'align': [] }],
      [{ 'list': 'ordered'}, { 'list': 'bullet' }],
      ['blockquote', 'code-block'],
      ['link'],
      ['clean']
    ]
  }
}

// 初始化编辑器
const initEditor = () => {
  if (editorContainer.value) {
    quillEditor.value = new Quill(editorContainer.value, editorOptions)
    
    // 设置初始内容
    if (props.modelValue) {
      quillEditor.value.root.innerHTML = props.modelValue
    }
    
    // 监听内容变化
    quillEditor.value.on('text-change', () => {
      const html = quillEditor.value.root.innerHTML
      emit('update:modelValue', html)
    })
  }
}

// 保存功能
const handleSave = async () => {
  if (!quillEditor.value) return
  
  isSaving.value = true
  try {
    const content = quillEditor.value.root.innerHTML
    emit('save', {
      content,
      type: props.documentType,
      wordCount: wordCount.value
    })
    
    // 模拟保存API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    lastSaved.value = new Date().toLocaleTimeString()
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
    console.error('Save error:', error)
  } finally {
    isSaving.value = false
  }
}

// 导出功能
const handleExport = async (format) => {
  if (!quillEditor.value) return
  
  try {
    const content = quillEditor.value.root.innerHTML
    emit('export', {
      content,
      format,
      type: props.documentType,
      wordCount: wordCount.value
    })
    
    ElMessage.success(`正在生成${format.toUpperCase()}文件...`)
  } catch (error) {
    ElMessage.error('导出失败')
    console.error('Export error:', error)
  }
}

// 分享功能
const handleShare = () => {
  shareDialogVisible.value = true
}

// 邀请协作者
const handleInvite = async () => {
  if (!shareEmail.value) {
    ElMessage.warning('请输入邮箱地址')
    return
  }
  
  try {
    // 这里调用邀请API
    ElMessage.success(`已向 ${shareEmail.value} 发送邀请`)
    shareEmail.value = ''
  } catch (error) {
    ElMessage.error('邀请发送失败')
  }
}

// 复制分享链接
const handleCopyLink = async () => {
  try {
    await navigator.clipboard.writeText(shareLink.value)
    ElMessage.success('链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 自动保存
let autoSaveTimer = null
const startAutoSave = () => {
  autoSaveTimer = setInterval(() => {
    if (quillEditor.value && props.modelValue) {
      handleSave()
    }
  }, 60000) // 每分钟自动保存
}

// 监听内容变化
watch(() => props.modelValue, (newValue) => {
  if (quillEditor.value && newValue !== quillEditor.value.root.innerHTML) {
    quillEditor.value.root.innerHTML = newValue
  }
})

// 生命周期
onMounted(() => {
  initEditor()
  startAutoSave()
})

onUnmounted(() => {
  if (autoSaveTimer) {
    clearInterval(autoSaveTimer)
  }
})
</script>

<style scoped>
/* 按钮样式 */
.primary-btn {
  background-color: #4F46E5 !important;
  color: #FFFFFF !important;
  border: none !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem 1rem !important;
  font-weight: 500 !important;
}

.primary-btn:hover {
  background-color: #4338CA !important;
}

.secondary-btn {
  background-color: transparent !important;
  color: #6B7280 !important;
  border: 1px solid #E5E7EB !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem 1rem !important;
}

.secondary-btn:hover {
  color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  background-color: rgba(79, 70, 229, 0.05) !important;
}

/* 卡片样式 */
.pro-card {
  background-color: #FFFFFF;
  border: none;
  border-radius: 0;
  box-shadow: none;
}

.pro-card-header {
  height: 3.5rem;
  padding: 0 1rem;
  border-bottom: 1px solid #E5E7EB;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.pro-card-body {
  padding: 1rem;
  flex: 1;
  min-height: 0;
}

/* 表单样式 */
.form-label {
  font-size: 14px;
  color: #374151;
  font-weight: 500;
  margin-bottom: 0.5rem;
  display: block;
}

.form-input {
  border-radius: 0.375rem !important;
}

/* 动画效果 */
.transition-standard {
  transition: all 0.2s ease;
}

.hover-scale:hover {
  transform: scale(1.02);
}

.button-click:active {
  transform: scale(0.98);
}

/* Quill编辑器样式覆盖 */
:deep(.ql-editor) {
  min-height: 0;
  height: 100%;
  font-size: 14px;
  line-height: 1.6;
  color: #374151;
  padding: 12px 15px;
  /* 添加宽度和换行限制 */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-wrap: break-word;
  word-wrap: break-word;
  white-space: pre-wrap;
  overflow-x: hidden;
  overflow-y: auto;
}

:deep(.ql-toolbar) {
  border: none;
  border-bottom: 1px solid #E5E7EB;
  padding: 0.5rem;
  flex-shrink: 0;
  /* 确保工具栏宽度限制 */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow-x: auto;
}

:deep(.ql-container) {
  border: none;
  font-family: inherit;
  height: 100%;
  display: flex;
  flex-direction: column;
  /* 添加容器宽度限制 */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  overflow: hidden;
}

:deep(.ql-container .ql-editor) {
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  /* 确保编辑器内容宽度限制 */
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

:deep(.ql-toolbar .ql-picker-label:hover),
:deep(.ql-toolbar .ql-picker-item:hover),
:deep(.ql-toolbar button:hover) {
  color: #4F46E5 !important;
}

:deep(.ql-toolbar button.ql-active),
:deep(.ql-toolbar .ql-picker-label.ql-active) {
  color: #4F46E5 !important;
}

/* 确保编辑器内容正确换行和处理 */
:deep(.ql-editor p),
:deep(.ql-editor div),
:deep(.ql-editor h1),
:deep(.ql-editor h2),
:deep(.ql-editor h3),
:deep(.ql-editor h4),
:deep(.ql-editor h5),
:deep(.ql-editor h6),
:deep(.ql-editor li),
:deep(.ql-editor span) {
  word-wrap: break-word;
  overflow-wrap: break-word;
  word-break: break-word;
  hyphens: auto;
  max-width: 100%;
}

/* 防止内容溢出 */
:deep(.ql-editor *) {
  max-width: 100% !important;
  box-sizing: border-box;
}

/* 特殊处理长链接和代码 */
:deep(.ql-editor a),
:deep(.ql-editor code) {
  word-break: break-all;
  overflow-wrap: break-word;
}

/* 对话框样式 */
.custom-dialog {
  box-shadow: none !important;
  border: 1px solid #E4E7ED;
}

:deep(.custom-dialog .el-dialog__header) {
  padding: 16px 24px;
  border-bottom: 1px solid #F1F5F9;
}

:deep(.custom-dialog .el-dialog__body) {
  padding: 24px;
}

:deep(.custom-dialog .el-dialog__footer) {
  padding: 16px 24px;
  border-top: 1px solid #F1F5F9;
}

/* Element Plus覆盖 */
:deep(.el-input.is-focus .el-input__wrapper) {
  border-color: #4F46E5 !important;
  box-shadow: 0 0 0 1px rgba(79, 70, 229, 0.8) !important;
}
</style> 