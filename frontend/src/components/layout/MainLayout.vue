<template>
  <div class="flex h-screen bg-gray-50">
    <!-- 侧边栏 -->
    <Sidebar />

    <!-- 主要内容区域 -->
    <div class="flex-1 flex flex-col overflow-hidden">
      <!-- 顶部导航栏 -->
      <Header />

      <!-- 内容区域 -->
      <main 
        class="flex-1 overflow-auto page-content-wrapper"
        :class="{ 'p-6': !isFullscreen }"
      >
        <router-view v-slot="{ Component }">
          <!-- 使用更快的过渡效果，减少过渡时间 -->
          <transition name="page-fade" mode="out-in" :duration="{ enter: 100, leave: 100 }">
            <component :is="Component" />
          </transition>
        </router-view>
      </main>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import Sidebar from './Sidebar.vue'
import Header from './Header.vue'

const route = useRoute()

// 检查当前路由是否需要全屏显示
const isFullscreen = computed(() => {
  return route.meta?.fullscreen === true
})
</script>

<style scoped>
/* 主布局样式 */

/* 页面切换动画 - 优化过渡效果 */
.page-fade-enter-active,
.page-fade-leave-active {
  transition: opacity 0.1s ease;
}

.page-fade-enter-from,
.page-fade-leave-to {
  opacity: 0;
}

/* 确保内容区域有相对定位，以便子元素可以使用绝对定位 */
.page-content-wrapper {
  position: relative;
}
</style>