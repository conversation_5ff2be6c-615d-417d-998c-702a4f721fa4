<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="handleDialogUpdate"
    title="专业详情"
    width="900px"
    :before-close="handleClose"
    class="custom-dialog"
    :show-close="false"
  >
    <!-- 自定义关闭按钮 -->
    <template #header="{ titleId, titleClass }">
      <div class="dialog-header">
        <span :id="titleId" :class="titleClass">专业详情</span>
        <button @click="handleClose" class="custom-close-btn">
          <span class="material-icons-outlined">close</span>
        </button>
      </div>
    </template>

    <div v-if="loading" class="loading-container">
      <el-skeleton animated>
        <template #template>
          <el-skeleton-item variant="h1" style="width: 60%; margin-bottom: 16px;" />
          <el-skeleton-item variant="h3" style="width: 40%; margin-bottom: 12px;" />
          <el-skeleton-item variant="text" style="width: 100%; margin-bottom: 8px;" />
          <el-skeleton-item variant="text" style="width: 80%; margin-bottom: 8px;" />
          <el-skeleton-item variant="text" style="width: 90%; margin-bottom: 8px;" />
        </template>
      </el-skeleton>
    </div>

    <div v-else-if="program" class="program-detail">
      <!-- 头部学校和专业信息 -->
      <div class="pro-card header-card">
        <div class="pro-card-body">
          <div class="header-layout">
            <div class="school-section">
              <h2 class="school-name">{{ program.school_name_cn }}</h2>
              <p v-if="program.school_name_en" class="school-name-en">{{ program.school_name_en }}</p>
              <div class="school-meta">
                <span class="region-tag">
                  <span class="material-icons-outlined">location_on</span>
                  {{ program.school_region }}
                </span>
                <span v-if="program.school_qs_rank" class="qs-rank">
                  <span class="material-icons-outlined">star</span>
                  QS {{ program.school_qs_rank }}
                </span>
                <span class="degree-badge">{{ program.degree }}</span>
              </div>
            </div>
            <div class="program-section">
              <h3 class="program-name">{{ program.program_name_cn }}</h3>
              <p v-if="program.program_name_en" class="program-name-en">{{ program.program_name_en }}</p>
              <div class="program-tags">
                <span v-if="program.program_direction" class="tag-item">{{ program.program_direction }}</span>
                <span v-if="program.program_category" class="tag-item category">{{ program.program_category }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 基本信息概览 -->
      <div class="pro-card">
        <div class="pro-card-header">
          <div class="pro-card-title">
            <span class="material-icons-outlined icon">info</span>
            基本信息
          </div>
        </div>
        <div class="pro-card-body">
          <div class="info-grid basic-info">
            <div class="info-item" v-if="program.faculty">
              <div class="info-label">
                <span class="material-icons-outlined">domain</span>
                所属学院
              </div>
              <div class="info-value">{{ program.faculty }}</div>
            </div>
            
            <div class="info-item" v-if="program.program_duration">
              <div class="info-label">
                <span class="material-icons-outlined">schedule</span>
                学制
              </div>
              <div class="info-value">{{ program.program_duration }}</div>
            </div>
            
            <div class="info-item" v-if="program.program_tuition">
              <div class="info-label">
                <span class="material-icons-outlined">payments</span>
                学费
              </div>
              <div class="info-value tuition">{{ program.program_tuition }}</div>
            </div>
            
            <div class="info-item full-width" v-if="program.application_time">
              <div class="info-label">
                <span class="material-icons-outlined">event</span>
                申请时间
              </div>
              <div class="info-value">{{ program.application_time }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 申请要求 -->
      <div class="pro-card">
        <div class="pro-card-header">
          <div class="pro-card-title">
            <span class="material-icons-outlined icon">assignment</span>
            申请要求
          </div>
        </div>
        <div class="pro-card-body">
          <div class="requirements-grid">
            <div class="info-item" v-if="program.gpa_requirements">
              <div class="info-label">
                <span class="material-icons-outlined">grade</span>
                GPA要求
              </div>
              <div class="info-value">{{ program.gpa_requirements }}</div>
            </div>
            
            <div class="info-item full-width" v-if="program.language_requirements">
              <div class="info-label">
                <span class="material-icons-outlined">language</span>
                语言要求
              </div>
              <div class="info-value">{{ program.language_requirements }}</div>
            </div>
          </div>
          
          <div v-if="program.application_requirements" class="info-item full-width">
            <div class="info-label">
              <span class="material-icons-outlined">description</span>
              详细申请要求
            </div>
            <div class="info-value">{{ program.application_requirements }}</div>
          </div>
        </div>
      </div>

      <!-- 专业目标 -->
      <div v-if="program.program_objectives" class="pro-card">
        <div class="pro-card-header">
          <div class="pro-card-title">
            <span class="material-icons-outlined icon">flag</span>
            专业目标
          </div>
        </div>
        <div class="pro-card-body">
          <div class="content-text">{{ program.program_objectives }}</div>
        </div>
      </div>

      <!-- 课程设置 -->
      <div v-if="program.courses" class="pro-card">
        <div class="pro-card-header">
          <div class="pro-card-title">
            <span class="material-icons-outlined icon">menu_book</span>
            课程设置
          </div>
        </div>
        <div class="pro-card-body">
          <div class="content-text">{{ program.courses }}</div>
        </div>
      </div>

      <!-- 其他费用信息 -->
      <div v-if="program.other_cost" class="pro-card">
        <div class="pro-card-header">
          <div class="pro-card-title">
            <span class="material-icons-outlined icon">receipt_long</span>
            其他费用
          </div>
        </div>
        <div class="pro-card-body">
          <div class="content-text">{{ program.other_cost }}</div>
        </div>
      </div>

      <!-- 学位认证 -->
      <div v-if="program.degree_evaluation" class="pro-card">
        <div class="pro-card-header">
          <div class="pro-card-title">
            <span class="material-icons-outlined icon">verified</span>
            学位认证
          </div>
        </div>
        <div class="pro-card-body">
          <div class="content-text">{{ program.degree_evaluation }}</div>
        </div>
      </div>
    </div>

    <div v-else class="error-state">
      <div class="pro-card">
        <div class="pro-card-body">
          <el-result
            icon="warning"
            title="加载失败"
            sub-title="无法获取专业详情信息"
          >
            <template #extra>
              <el-button type="primary" @click="loadProgramDetail" class="btn-primary">
                <span class="material-icons-outlined">refresh</span>
                重试
              </el-button>
            </template>
          </el-result>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button v-if="program?.program_website" @click="handleViewWebsite" class="btn-website">
          <span class="material-icons-outlined">language</span>
          查看官网
        </el-button>

        <el-button type="primary" @click="handleCollect" class="btn-primary button-click">
          <span class="material-icons-outlined">bookmark_border</span>
          收藏专业
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getProgramDetail } from '@/api/programs'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  programId: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['update:visible', 'collect'])

const loading = ref(false)
const program = ref(null)

// 处理弹窗显示/隐藏更新
const handleDialogUpdate = (value) => {
  emit('update:visible', value)
}

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal && props.programId) {
    loadProgramDetail()
  } else if (!newVal) {
    // 弹窗关闭时延迟清空数据，避免短暂显示错误状态
    setTimeout(() => {
      program.value = null
    }, 300)
  }
})

// 监听程序ID变化
watch(() => props.programId, (newVal) => {
  if (newVal && props.visible) {
    loadProgramDetail()
  }
})

// 加载专业详情
const loadProgramDetail = async () => {
  if (!props.programId) return
  
  loading.value = true
  try {
    program.value = await getProgramDetail(props.programId)
  } catch (error) {
    console.error('加载专业详情失败:', error)
    ElMessage.error('加载专业详情失败')
    program.value = null
  } finally {
    loading.value = false
  }
}

// 关闭弹窗
const handleClose = () => {
  emit('update:visible', false)
  // 延迟清空数据，避免短暂显示加载失败画面
  setTimeout(() => {
    program.value = null
  }, 300)
}

// 收藏专业
const handleCollect = () => {
  if (program.value) {
    emit('collect', program.value)
  }
}

// 查看官网
const handleViewWebsite = () => {
  if (program.value?.program_website) {
    let url = program.value.program_website
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url
    }
    window.open(url, '_blank')
  }
}
</script>

<style scoped>
/* === 全局变量覆盖 === */
:root {
  --el-color-primary: #4F46E5 !important;
  --el-color-primary-light-3: #6366F1 !important;
  --el-color-primary-light-5: #818CF8 !important;
  --el-color-primary-light-7: #C7D2FE !important;
  --el-color-primary-light-9: #EEF2FF !important;
  --el-color-primary-dark-2: #4338CA !important;
}

/* === 弹窗系统 === */
:deep(.custom-dialog) {
  box-shadow: none !important;
  border: 1px solid #E4E7ED;
}

:deep(.custom-dialog .el-dialog__header) {
  padding: 12px 20px;
  border-bottom: 1px solid #F1F5F9;
  font-size: 14px;
}

:deep(.custom-dialog .el-dialog__body) {
  padding: 16px;
  max-height: 80vh;
  overflow-y: auto;
}

:deep(.custom-dialog .el-dialog__footer) {
  padding: 12px 20px;
  border-top: 1px solid #F1F5F9;
}

/* === 加载状态 === */
.loading-container {
  padding: 1.5rem;
}

/* === 对话框头部 === */
.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0;
}

/* === 自定义关闭按钮 === */
.custom-close-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 50%;
  background: #4F46E5;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 6px rgba(79, 70, 229, 0.2);
  flex-shrink: 0;
}

.custom-close-btn:hover {
  background: #4338CA;
  transform: scale(1.1);
  box-shadow: 0 3px 8px rgba(79, 70, 229, 0.3);
}

.custom-close-btn:active {
  background: #3730A3;
  transform: scale(0.95);
}

.custom-close-btn .material-icons-outlined {
  font-size: 16px;
  color: white;
}

/* === 卡片系统 === */
.pro-card {
  background-color: #FFFFFF;
  border-radius: 0.375rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #E5E7EB;
  margin-bottom: 0.5rem;
}

.pro-card:last-child {
  margin-bottom: 0;
}

.pro-card-header {
  height: 2.25rem;
  padding: 0 0.75rem;
  border-bottom: 1px solid #E5E7EB;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.pro-card-title {
  color: #1F2937;
  font-weight: 600;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
}

.pro-card-title .icon {
  margin-right: 0.375rem;
  color: #4F46E5;
  font-size: 1rem;
}

.pro-card-body {
  padding: 0.75rem;
}

/* === 头部布局 === */
.header-card {
  border: none;
  box-shadow: none;
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
}

.header-layout {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  align-items: start;
}

.school-section {
  padding-right: 0.75rem;
  border-right: 1px solid #E2E8F0;
}

.program-section {
  padding-left: 0.75rem;
}

/* === 学校信息 === */
.school-name {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1F2937;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.school-name-en {
  color: #6B7280;
  margin: 0 0 0.5rem 0;
  font-style: italic;
  font-size: 0.8rem;
}

.school-meta {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.region-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  color: #6B7280;
  font-size: 0.75rem;
}

.qs-rank {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: #FEF3C7;
  color: #92400E;
  padding: 0.125rem 0.5rem;
  border-radius: 0.75rem;
  font-size: 0.7rem;
  font-weight: 500;
}

.degree-badge {
  background: #4F46E5;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 0.75rem;
  font-weight: 500;
  font-size: 0.7rem;
}

/* === 专业信息 === */
.program-name {
  font-size: 1rem;
  font-weight: 600;
  color: #1F2937;
  margin: 0 0 0.25rem 0;
  line-height: 1.3;
}

.program-name-en {
  color: #6B7280;
  font-size: 0.8rem;
  margin: 0 0 0.5rem 0;
  font-style: italic;
}

.program-tags {
  display: flex;
  gap: 0.375rem;
  flex-wrap: wrap;
}

.tag-item {
  background: #EEF2FF;
  color: #4F46E5;
  padding: 0.125rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.7rem;
  font-weight: 500;
  border: 1px solid #C7D2FE;
}

.tag-item.category {
  background: #F0F9FF;
  color: #0EA5E9;
  border-color: #BAE6FD;
}

/* === 基本信息网格 === */
.info-grid {
  display: grid;
  gap: 0.5rem;
}

.basic-info {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.info-item {
  padding: 0.5rem;
  background: #F9FAFB;
  border-radius: 0.375rem;
  border: 1px solid #F1F5F9;
}

.info-label {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.7rem;
  font-weight: 500;
  color: #6B7280;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  margin-bottom: 0.2rem;
}

.info-label .material-icons-outlined {
  font-size: 14px;
  color: #4F46E5;
}

.info-value {
  font-size: 0.8rem;
  color: #1F2937;
  font-weight: 500;
}

.info-value.tuition {
  color: #0EA5E9;
  font-weight: 600;
}

/* === 申请要求网格 === */
.requirements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.info-item.full-width {
  grid-column: 1 / -1;
}

/* === 内容文本 === */
.content-text {
  color: #374151;
  line-height: 1.5;
  font-size: 0.8rem;
}



/* === 按钮系统 === */
.btn-primary {
  background-color: #4F46E5 !important;
  color: #FFFFFF !important;
  border-radius: 0.375rem !important;
  padding: 0.375rem 0.75rem !important;
  font-weight: 500 !important;
  font-size: 0.8rem !important;
  border: none !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.25rem !important;
}

.btn-primary:hover {
  background-color: #4338CA !important;
  transform: scale(1.02);
}

.btn-primary:active {
  background-color: #3730A3 !important;
}

.btn-secondary {
  background-color: transparent !important;
  color: #6B7280 !important;
  border: 1px solid #E5E7EB !important;
  padding: 0.375rem 0.75rem !important;
  border-radius: 0.375rem !important;
  font-size: 0.8rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.25rem !important;
}

.btn-secondary:hover {
  color: #4F46E5 !important;
  border-color: #4F46E5 !important;
  background-color: rgba(79, 70, 229, 0.05) !important;
}

.btn-website {
  background-color: #4F46E5 !important;
  color: #FFFFFF !important;
  border: none !important;
  border-radius: 0.375rem !important;
  font-size: 0.8rem !important;
  font-weight: 500 !important;
  padding: 0.375rem 0.75rem !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.25rem !important;
}

.btn-website:hover {
  background-color: #4338CA !important;
}

.btn-website:active {
  background-color: #3730A3 !important;
}

/* === 动画系统 === */
.button-click {
  transition: transform 0.1s ease;
}

.button-click:active {
  transform: scale(0.98);
}

/* === 图标系统 === */
.material-icons-outlined {
  font-family: 'Material Icons Outlined';
  font-size: 16px;
  line-height: 1;
  transition: color 0.2s ease;
}

/* === 弹窗底部 === */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
}

/* === 错误状态 === */
.error-state {
  padding: 1rem 0;
}

/* === 响应式设计 === */
@media (max-width: 768px) {
  .header-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .school-section {
    padding-right: 0;
    border-right: none;
    border-bottom: 1px solid #E2E8F0;
    padding-bottom: 1rem;
  }

  .program-section {
    padding-left: 0;
    padding-top: 1rem;
  }

  .requirements-grid {
    grid-template-columns: 1fr;
  }

  .dialog-footer {
    flex-direction: column;
    gap: 0.375rem;
  }

  .dialog-footer .btn-primary,
  .dialog-footer .btn-secondary,
  .dialog-footer .btn-website {
    width: 100%;
    justify-content: center;
  }
}

/* === 滚动条样式 === */
:deep(.el-dialog__body)::-webkit-scrollbar {
  width: 6px;
}

:deep(.el-dialog__body)::-webkit-scrollbar-track {
  background: #F1F1F1;
  border-radius: 3px;
}

:deep(.el-dialog__body)::-webkit-scrollbar-thumb {
  background: #C1C1C1;
  border-radius: 3px;
}

:deep(.el-dialog__body)::-webkit-scrollbar-thumb:hover {
  background: #A8A8A8;
}
</style> 