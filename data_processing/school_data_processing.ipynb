{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import re\n", "import json\n", "import pandas as pd\n", "from pathlib import Path\n", "\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import wordninja\n", "from thefuzz import fuzz, process"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["case_df = pd.read_csv('案例数据库.csv')\n", "program_df = pd.read_csv('专业数据库.csv')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 境内院校数据库基本制备"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 院校中英文名称提取"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["# unique_schools = program_df.drop_duplicates(subset=['学校中文名', '学校英文名'])\n", "# en_school_dict = dict(zip(unique_schools['学校中文名'], unique_schools['学校英文名']))\n", "\n", "# cn_school_dict = {name: '' for name in cn_school_df['学校名称']}\n", "# merged_school_dict = {**cn_school_dict,**en_school_dict}\n", "# with open('院校数据/schools.json', 'w', encoding='utf-8') as f:\n", "#     json.dump(merged_school_dict, f, ensure_ascii=False, indent=4)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 软科榜单处理"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# 读取Excel文件\n", "file_path = '院校数据/2025年软科中国大学排名.xlsx'\n", "xls = pd.ExcelFile(file_path)\n", "\n", "# 初始化一个空的DataFrame用于存储最终结果\n", "ruanke_df = pd.DataFrame()\n", "\n", "# 定义需要处理的子表名列表\n", "sheet_names = ['主榜', '医药类', '财经类', '语言类', '政法类', '民族类', '体育类', '合作办学大学']\n", "\n", "for sheet_name in sheet_names:\n", "    # 检查工作表是否存在\n", "    if sheet_name not in xls.sheet_names:\n", "        print(f\"Sheet '{sheet_name}' 不存在于Excel文件中\")\n", "        continue\n", "    \n", "    # 读取每个子表的数据\n", "    df = pd.read_excel(xls, sheet_name)\n", "    \n", "    if sheet_name == '主榜':\n", "        # 提取主榜中的特定列\n", "        columns_to_keep = ['2025排名', '学校名称', '学校类型']\n", "    else:\n", "        # 提取其他子表中的特定列\n", "        columns_to_keep = ['主榜参考排名', '学校名称', '学校类型']\n", "    \n", "    # 检查是否存在所需的列\n", "    existing_columns = set(df.columns).intersection(columns_to_keep)\n", "    if not existing_columns:\n", "        print(f\"Sheet '{sheet_name}' 缺少所需列: {columns_to_keep}\")\n", "        continue\n", "    \n", "    # 提取并重命名列\n", "    sub_df = df[list(existing_columns)].copy()\n", "    sub_df.rename(columns={'2025排名': '排名', '主榜参考排名': '排名'}, inplace=True)\n", "    \n", "    # 将子表数据追加到最终的DataFrame中\n", "    ruanke_df = pd.concat([ruanke_df, sub_df], ignore_index=True)\n", "\n", "# 数据清洗处理\n", "ruanke_df.dropna(how='all', inplace=True)\n", "\n", "# 将'排名'列中所有值为'500+'的变为500\n", "ruanke_df['排名'] = ruanke_df['排名'].apply(lambda x: 500 if x == '500+' else x)\n", "\n", "# 将'排名'列转换为int类型\n", "ruanke_df['排名'] = ruanke_df['排名'].astype(int)\n", "\n", "# 按排名排序\n", "ruanke_df.sort_values(by='排名', inplace=True)\n", "\n", "# 重置index\n", "ruanke_df.reset_index(drop=True, inplace=True)\n", "\n", "# ruanke_df.to_csv(\"境内院校数据库.csv\", index=False, encoding='utf-8-sig')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 软科表与教育部官方院校名单表合并"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校类型</th>\n", "      <th>排名</th>\n", "      <th>学校名称</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>530</th>\n", "      <td>医药</td>\n", "      <td>376</td>\n", "      <td>内蒙古科技大学包头医学院</td>\n", "    </tr>\n", "    <tr>\n", "      <th>634</th>\n", "      <td>综合</td>\n", "      <td>460</td>\n", "      <td>内蒙古科技大学包头师范学院</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    学校类型   排名           学校名称\n", "530   医药  376   内蒙古科技大学包头医学院\n", "634   综合  460  内蒙古科技大学包头师范学院"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["cn_school_df = pd.read_excel('./院校数据/境内院校.xlsx')\n", "ruanke_df[~ruanke_df['学校名称'].isin(cn_school_df['学校名称'])]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# 处理 cn_school_df\n", "# 将 '学校标识码' 列转换为 str 类型\n", "cn_school_df['学校标识码'] = cn_school_df['学校标识码'].astype(str)\n", "\n", "# 去掉 '办学层次' 列\n", "if '办学层次' in cn_school_df.columns:\n", "    cn_school_df.drop(columns=['办学层次'], inplace=True)\n", "\n", "# 将 '排名' 列重命名为 '软科排名'\n", "if '排名' in ruanke_df.columns:\n", "    ruanke_df.rename(columns={'排名': '软科排名'}, inplace=True)\n", "\n", "# 合并两个 DataFrame，以 outer 方式连接\n", "merged_cn_school_df = pd.merge(ruanke_df, cn_school_df, on='学校名称', how='outer')\n", "\n", "# 按 '软科排名' 排序，NaN 值排在最后\n", "merged_cn_school_df.sort_values(by='软科排名', ascending=True, na_position='last', inplace=True)\n", "merged_cn_school_df['软科排名'] = merged_cn_school_df['软科排名'].astype('Int64')\n", "\n", "# 重置 index\n", "merged_cn_school_df.reset_index(drop=True, inplace=True)\n", "\n", "# 重新排列列的顺序\n", "merged_cn_school_df = merged_cn_school_df[['学校名称', '学校类型', '学校标识码', '所在地', '主管部门', '软科排名', '备注']]"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>学校名称</th>\n", "      <th>学校类型</th>\n", "      <th>学校标识码</th>\n", "      <th>所在地</th>\n", "      <th>主管部门</th>\n", "      <th>软科排名</th>\n", "      <th>备注</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>清华大学</td>\n", "      <td>综合</td>\n", "      <td>4111010003</td>\n", "      <td>北京市</td>\n", "      <td>教育部</td>\n", "      <td>1</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>北京大学</td>\n", "      <td>综合</td>\n", "      <td>4111010001</td>\n", "      <td>北京市</td>\n", "      <td>教育部</td>\n", "      <td>2</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>浙江大学</td>\n", "      <td>综合</td>\n", "      <td>4133010335</td>\n", "      <td>杭州市</td>\n", "      <td>教育部</td>\n", "      <td>3</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>上海交通大学</td>\n", "      <td>综合</td>\n", "      <td>4131010248</td>\n", "      <td>上海市</td>\n", "      <td>教育部</td>\n", "      <td>4</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>复旦大学</td>\n", "      <td>综合</td>\n", "      <td>4131010246</td>\n", "      <td>上海市</td>\n", "      <td>教育部</td>\n", "      <td>5</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1305</th>\n", "      <td>黑龙江工程学院昆仑旅游学院</td>\n", "      <td>NaN</td>\n", "      <td>4123013304</td>\n", "      <td>哈尔滨市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1306</th>\n", "      <td>黑龙江财经学院</td>\n", "      <td>NaN</td>\n", "      <td>4123013298</td>\n", "      <td>哈尔滨市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1307</th>\n", "      <td>齐鲁医药学院</td>\n", "      <td>NaN</td>\n", "      <td>4137010825</td>\n", "      <td>淄博市</td>\n", "      <td>山东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1308</th>\n", "      <td>齐鲁理工学院</td>\n", "      <td>NaN</td>\n", "      <td>4137013998</td>\n", "      <td>济南市</td>\n", "      <td>山东省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1309</th>\n", "      <td>齐齐哈尔工程学院</td>\n", "      <td>NaN</td>\n", "      <td>4123012729</td>\n", "      <td>齐齐哈尔市</td>\n", "      <td>黑龙江省教育厅</td>\n", "      <td>&lt;NA&gt;</td>\n", "      <td>民办</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>1310 rows × 7 columns</p>\n", "</div>"], "text/plain": ["               学校名称 学校类型       学校标识码    所在地     主管部门  软科排名   备注\n", "0              清华大学   综合  4111010003    北京市      教育部     1  NaN\n", "1              北京大学   综合  4111010001    北京市      教育部     2  NaN\n", "2              浙江大学   综合  4133010335    杭州市      教育部     3  NaN\n", "3            上海交通大学   综合  4131010248    上海市      教育部     4  NaN\n", "4              复旦大学   综合  4131010246    上海市      教育部     5  NaN\n", "...             ...  ...         ...    ...      ...   ...  ...\n", "1305  黑龙江工程学院昆仑旅游学院  NaN  4123013304   哈尔滨市  黑龙江省教育厅  <NA>   民办\n", "1306        黑龙江财经学院  NaN  4123013298   哈尔滨市  黑龙江省教育厅  <NA>   民办\n", "1307         齐鲁医药学院  NaN  4137010825    淄博市   山东省教育厅  <NA>   民办\n", "1308         齐鲁理工学院  NaN  4137013998    济南市   山东省教育厅  <NA>   民办\n", "1309       齐齐哈尔工程学院  NaN  4123012729  齐齐哈尔市  黑龙江省教育厅  <NA>   民办\n", "\n", "[1310 rows x 7 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_cn_school_df"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [], "source": ["# merged_cn_school_df.to_csv(\"境内院校数据库.csv\", index=False, encoding='utf-8-sig')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "tunshu_data", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 2}