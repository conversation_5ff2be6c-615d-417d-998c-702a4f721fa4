import os
import pandas as pd
import argparse
from sqlalchemy import Column, Integer, String, Text, create_engine, text, Float, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy_utils import database_exists, create_database
from pathlib import Path
from typing import Dict, Any, Type, Optional

# 声明SQLAlchemy基类
Base = declarative_base()

class AISelectionProgram(Base):
    """
    专业数据库模型类，对应 PostgreSQL 数据库中的 ai_selection_programs 表
    存储专业信息数据
    """
    __tablename__ = "ai_selection_programs"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="ID，自增主键")
    
    # 学校信息
    school_name_cn = Column(String(200), nullable=False, index=True, comment="学校中文名")
    school_name_en = Column(String(200), nullable=True, index=True, comment="学校英文名")
    school_qs_name = Column(String(200), nullable=True, comment="学校QS英文名")
    school_qs_rank = Column(String(100), nullable=True, comment="学校QS排名")
    school_region = Column(String(100), nullable=True, comment="学校所在地区")

    # 专业信息
    program_code = Column(Integer, nullable=True, comment="专业代码")
    degree = Column(String(50), nullable=True, comment="申请学位类型")
    program_name_cn = Column(String(200), nullable=False, index=True, comment="专业中文名")
    program_name_en = Column(String(200), nullable=True, index=True, comment="专业英文名")
    program_category = Column(String(100), nullable=True, comment="专业大类")
    program_direction = Column(String(200), nullable=True, comment="专业方向")
    faculty = Column(String(200), nullable=True, comment="所在学院")
    
    enrollment_time = Column(String(100), nullable=True, comment="入学时间")
    program_duration = Column(String(100), nullable=True, comment="项目时长")
    program_tuition = Column(String(100), nullable=True, comment="项目学费")
    application_time = Column(Text, nullable=True, comment="申请时间")
    application_requirements = Column(Text, nullable=True, comment="申请要求")
    gpa_requirements = Column(Float, nullable=True, comment="绩点要求")
    language_requirements = Column(Text, nullable=True, comment="语言要求")

    program_objectives = Column(Text, nullable=True, comment="培养目标")
    courses = Column(Text, nullable=True, comment="课程设置")

    # 其他信息
    program_website = Column(String(500), nullable=True, comment="项目官网")
    other_cost = Column(String(100), nullable=True, comment="年开销预估值")
    degree_evaluation = Column(Text, nullable=True, comment="留服认证")
    embedding = Column(JSONB, nullable=True, comment="专业描述的向量嵌入")

class AISelectionCase(Base):
    """
    案例数据库模型类，对应 PostgreSQL 数据库中的 ai_selection_cases 表
    存储学生案例信息数据
    """
    __tablename__ = "ai_selection_cases"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="ID，自增主键")
    
    # 录取信息
    offer_school = Column(String(200), nullable=True, comment="录取学校")
    offer_program = Column(String(200), nullable=True, comment="录取项目")
    offer_program_id = Column(Integer, nullable=True, comment="录取项目ID")
    offer_program_code = Column(Integer, nullable=True, comment="录取项目代码")
    offer_region = Column(String(100), nullable=True, comment="录取地区")
    offer_degree = Column(String(50), nullable=True, comment="录取学位")
    offer_major_direction = Column(String(200), nullable=True, comment="录取专业方向")
    
    # 学生基本信息
    student_name = Column(String(100), nullable=True, comment="学生姓名")
    undergraduate_school = Column(String(200), nullable=True, comment="本科学校")
    undergraduate_school_tier = Column(String(50), nullable=True, comment="本科学校层次")
    undergraduate_major = Column(String(200), nullable=True, comment="本科专业")
    gpa = Column(Float, nullable=True, comment="绩点")
    language_score = Column(String(200), nullable=True, comment="语言成绩")
    key_experiences = Column(Text, nullable=True, comment="关键经历描述")
    embedding = Column(JSONB, nullable=True, comment="案例描述的向量嵌入")

class AISelectionHomeSchool(Base):
    """
    境内院校数据库模型类，对应 PostgreSQL 数据库中的 ai_selection_home_schools 表
    存储境内院校信息数据
    """
    __tablename__ = "ai_selection_home_schools"

    # 基本字段
    id = Column(Integer, primary_key=True, index=True, comment="ID，自增主键")
    
    # 学校基本信息
    school_name = Column(String(200), nullable=False, index=True, comment="学校名称")
    school_type = Column(String(100), nullable=True, comment="学校类型")
    school_code = Column(String(50), nullable=True, index=True, comment="学校标识码")
    location = Column(String(100), nullable=True, comment="所在地")
    authority = Column(String(200), nullable=True, comment="主管部门")
    ranking_ruanke = Column(Integer, nullable=True, comment="软科排名")
    remarks = Column(Text, nullable=True, comment="备注")

# 表配置字典 - 在这里注册所有支持的表类型
TABLE_CONFIGS = {
    'programs': {
        'model': AISelectionProgram,
        'default_csv': '专业数据库.csv',
        'column_mapping': {
            '学校中文名': 'school_name_cn',
            '学校英文名': 'school_name_en',
            '专业中文名': 'program_name_cn',
            '专业英文名': 'program_name_en',
            '专业大类': 'program_category',
            '专业方向': 'program_direction',
            '所在学院': 'faculty',
            '入学时间': 'enrollment_time',
            '项目时长': 'program_duration',
            '项目官网': 'program_website',
            '培养目标': 'program_objectives',
            '申请要求': 'application_requirements',
            '语言要求': 'language_requirements',
            '申请时间': 'application_time',
            '课程设置': 'courses',
            '专业代码': 'program_code',
            '项目学费': 'program_tuition',
            '学校英文名(QS26)': 'school_qs_name',
            '学校排名(QS26)': 'school_qs_rank',
            '学校所在地区': 'school_region',
            '申请学位类型': 'degree',
            '绩点要求': 'gpa_requirements',
            '年开销预估值': 'other_cost',
            '留服认证': 'degree_evaluation',
        },
        'dtype_mapping': {},
        'description': '专业数据表'
    },
    'cases': {
        'model': AISelectionCase,
        'default_csv': '案例数据库.csv',
        'column_mapping': {},  # 案例表的CSV列名已与数据库列名一致
        'dtype_mapping': {
            'offer_program_id': 'Int64',
            'offer_program_code': 'Int64'
        },
        'description': '案例数据表'
    },
    'home_schools': {
        'model': AISelectionHomeSchool,
        'default_csv': '境内院校数据库.csv',
        'column_mapping': {
            '学校名称': 'school_name',
            '学校类型': 'school_type',
            '学校标识码': 'school_code',
            '所在地': 'location',
            '主管部门': 'authority',
            '软科排名': 'ranking_ruanke',
            '备注': 'remarks'
        },
        'dtype_mapping': {
            '学校标识码': 'str',
            '软科排名': 'Int64'  # 确保软科排名为Int64类型, 注意要用映射前的名字，因为先read_csv后才mapping
        },
        'description': '境内院校数据表'
    }
}


def get_database_url(user: str = "postgres", password: str = "admin123", 
                    host: str = "localhost", port: str = "5432", 
                    db_name: str = "tunshuedu_ai_selection_db") -> str:
    """构建数据库连接URL"""
    return f"postgresql://{user}:{password}@{host}:{port}/{db_name}"


def create_table(engine, model_class: Type[Base]) -> None:
    """创建指定的数据库表"""
    # 先删除指定表（如果存在），然后重新创建
    model_class.__table__.drop(engine, checkfirst=True)
    print(f"已删除旧表 {model_class.__tablename__}（如果存在）")
    
    # 只创建指定的表
    model_class.__table__.create(engine, checkfirst=True)
    print(f"表 {model_class.__tablename__} 创建成功！")


def import_csv_to_db(csv_path: str, engine, table_type: str) -> None:
    """导入CSV数据到数据库"""
    if table_type not in TABLE_CONFIGS:
        raise ValueError(f"不支持的表类型: {table_type}. 支持的类型: {list(TABLE_CONFIGS.keys())}")
    
    config = TABLE_CONFIGS[table_type]
    model_class = config['model']
    column_mapping = config['column_mapping']
    dtype_mapping = config['dtype_mapping']
    
    try:
        # 读取CSV文件
        read_kwargs = {'dtype': dtype_mapping} if dtype_mapping else {}
        df = pd.read_csv(csv_path, **read_kwargs)
        print(f"成功读取CSV文件，包含 {len(df)} 行数据")
        
        # 应用列名映射（如果需要）
        if column_mapping:
            df = df.rename(columns=column_mapping)
            print("已应用列名映射")
        else:
            print("CSV列名已与数据库列名一致，跳过列名映射步骤")
        
        # 创建数据库会话
        Session = sessionmaker(bind=engine)
        session = Session()
        
        try:
            # 开始导入数据
            print(f"开始导入数据到表 {model_class.__tablename__}")
            
            # 分批处理，每次处理1000条记录
            batch_size = 1000
            total_records = len(df)
            
            for i in range(0, total_records, batch_size):
                end = min(i + batch_size, total_records)
                batch_df = df.iloc[i:end]
                
                # 将DataFrame转换为字典列表
                records = batch_df.to_dict(orient='records')
                
                # 批量插入数据库
                session.bulk_insert_mappings(model_class, records)
                session.commit()
                
                print(f"成功导入记录 {i+1} 到 {end} (共 {total_records} 条)")
                
            print(f"所有数据导入完成！总共导入了 {total_records} 条记录")
        
        except Exception as e:
            session.rollback()
            print(f"导入数据时发生错误: {str(e)}")
            raise
        finally:
            session.close()
            
    except Exception as e:
        print(f"处理CSV文件时发生错误: {str(e)}")
        raise


def list_supported_tables() -> None:
    """列出所有支持的表类型"""
    print("支持的表类型:")
    for table_type, config in TABLE_CONFIGS.items():
        print(f"  - {table_type}: {config['description']} (默认CSV: {config['default_csv']})")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description='通用CSV数据导入PostgreSQL数据库工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python csv_to_postgres.py --table programs --csv 专业数据库.csv
  python csv_to_postgres.py --table cases --csv 案例数据库.csv
  python csv_to_postgres.py --list-tables  # 查看支持的表类型
        """
    )
    
    # 表类型参数
    parser.add_argument('--table', type=str, 
                        help=f'要导入的表类型，支持: {list(TABLE_CONFIGS.keys())}')
    parser.add_argument('--list-tables', action='store_true',
                        help='列出所有支持的表类型')
    
    # CSV文件参数
    parser.add_argument('--csv', type=str,
                        help='CSV文件路径（如不指定，使用对应表类型的默认文件）')
    
    # 数据库连接参数
    parser.add_argument('--db-user', type=str, default='postgres',
                        help='数据库用户名 (默认: postgres)')
    parser.add_argument('--db-password', type=str, default='admin123',
                        help='数据库密码 (默认: admin123)')
    parser.add_argument('--db-host', type=str, default='localhost',
                        help='数据库主机 (默认: localhost)')
    parser.add_argument('--db-port', type=str, default='5432',
                        help='数据库端口 (默认: 5432)')
    parser.add_argument('--db-name', type=str, default='tunshuedu_ai_selection_db',
                        help='数据库名称 (默认: tunshuedu_ai_selection_db)')
    
    args = parser.parse_args()
    
    # 如果请求列出表类型
    if args.list_tables:
        list_supported_tables()
        return
    
    # 检查必需参数
    if not args.table:
        print("错误: 必须指定表类型 (--table)")
        print("使用 --list-tables 查看支持的表类型")
        return
    
    if args.table not in TABLE_CONFIGS:
        print(f"错误: 不支持的表类型 '{args.table}'")
        print("使用 --list-tables 查看支持的表类型")
        return
    
    # 确定CSV文件路径
    if args.csv:
        csv_filename = args.csv
    else:
        csv_filename = TABLE_CONFIGS[args.table]['default_csv']
        print(f"使用默认CSV文件: {csv_filename}")
    
    script_dir = Path(__file__).parent
    csv_path = script_dir / csv_filename
    
    if not csv_path.exists():
        print(f"错误: CSV文件 {csv_path} 不存在!")
        return
    
    # 构建数据库连接URL
    db_url = get_database_url(
        user=args.db_user,
        password=args.db_password,
        host=args.db_host,
        port=args.db_port,
        db_name=args.db_name
    )
    
    # 创建数据库引擎
    engine = create_engine(
        db_url,
        connect_args={
            "client_encoding": "utf8",
        },
    )
    
    # 如果数据库不存在，创建数据库
    if not database_exists(engine.url):
        create_database(engine.url, encoding='utf8')
        print(f"数据库 {args.db_name} 已创建（UTF-8编码）")
    
    # 获取表配置
    table_config = TABLE_CONFIGS[args.table]
    model_class = table_config['model']
    
    # 创建表
    create_table(engine, model_class)
    
    # 导入数据
    import_csv_to_db(csv_path, engine, args.table)
    print(f"{table_config['description']}数据导入过程完成!")


if __name__ == "__main__":
    main()
    
    # 使用示例:
    # python csv_to_postgres.py --table programs --db-password="123456789"
    # python csv_to_postgres.py --table programs --db-name tunshuedu_db

    # python csv_to_postgres.py --table cases --db-password="123456789"
    # python csv_to_postgres.py --table cases --db-name tunshuedu_db

    # python csv_to_postgres.py --table home_schools --db-password="123456789"
    # python csv_to_postgres.py --table home_schools --db-name tunshuedu_db
    
    # python csv_to_postgres.py --list-tables 