# TunshuEdu - 留学行业的AI工具箱

TunshuEdu (囤鼠科技教育平台) 是一个专为留学教育行业设计的综合AI工具平台，帮助留学顾问管理客户、撰写文书、规划申请策略等。

## 🚀 项目概述

本项目采用现代化前后端分离架构：
- **前端**: Vue 3 + Vite + TailwindCSS + Element Plus
- **后端**: Python FastAPI + SQLAlchemy（异步）
- **数据库**: PostgreSQL
- **AI集成**: 大语言模型 + 向量检索(RAG)

## 🎯 核心功能

### 客户管理系统
- 📋 客户信息管理（基本信息、教育背景、工作经历等）
- 📊 客户档案可视化展示
- 🔍 智能搜索和筛选
- 📁 文档上传和管理

### AI智能选校
- 🎓 基于用户背景的个性化院校推荐
- 📈 匹配度评分和排名
- 📑 历史案例分析对比
- 🔄 多维度筛选和对比

### 留学数据库
- 📚 项目数据库（院校专业信息查询）
- 🏫 学校排名和详细信息
- 🔍 多维度搜索和筛选功能
- 📊 院校和专业统计数据

### 文书写作助手
- 📝 推荐信智能生成
- 📖 个人陈述辅助写作
- 📄 简历优化建议
- ✏️ 文书智能润色

### AI工具集
- 🤖 AI率检测（识别AI生成内容）
- 🎨 AI率降低（优化文本自然度）
- 🔧 文本智能优化
- 🌐 多语言支持

### 用户认证系统
- 🔐 安全的JWT认证
- 👤 用户权限管理
- 🔄 Token自动刷新
- 📱 响应式设计

## 🛠️ 技术栈

### 前端技术
- **框架**: Vue 3 (组合式API)
- **构建工具**: Vite
- **样式**: TailwindCSS + Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **HTTP客户端**: Axios
- **类型检查**: TypeScript

### 后端技术
- **API框架**: FastAPI
- **ORM**: SQLAlchemy (异步)
- **数据验证**: Pydantic
- **认证**: JWT + OAuth2
- **数据库驱动**: asyncpg
- **服务器**: Uvicorn (ASGI)

### 数据库
- **主数据库**: PostgreSQL 12+
- **特性**: 支持JSONB、全文搜索、向量存储

### AI技术
- **大语言模型**: 支持多种LLM API
- **向量检索**: RAG (检索增强生成)
- **文本处理**: NLP工具链

## 📁 项目结构

```
TunshuEdu/
├── backend/                 # 后端代码 (Python FastAPI)
│   ├── alembic.ini          # Alembic 配置文件
│   ├── app/                 # FastAPI 应用核心目录
│   │   ├── ai_selection/      # AI选校模块
│   │   │   ├── api/
│   │   │   │   ├── endpoints/
│   │   │   │   ├── __init__.py
│   │   │   │   └── router.py
│   │   │   ├── core/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── candidate_pool.py
│   │   │   │   ├── program_matching.py
│   │   │   │   ├── ranking.py
│   │   │   │   ├── school_matching.py
│   │   │   │   └── user_profile.py
│   │   │   ├── db/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── init_db.py
│   │   │   │   ├── models.py
│   │   │   │   └── seed.py
│   │   │   ├── schemas/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── case.py
│   │   │   │   ├── program.py
│   │   │   │   ├── recommendation.py
│   │   │   │   └── user.py
│   │   │   ├── utils/
│   │   │   │   ├── __init__.py
│   │   │   │   ├── llm.py
│   │   │   │   └── rag.py
│   │   │   ├── __init__.py
│   │   │   └── README.md
│   │   ├── api/             # API 路由 (按功能模块划分)
│   │   │   ├── __init__.py
│   │   │   ├── auth.py
│   │   │   ├── clients.py
│   │   │   └── dashboard.py
│   │   ├── core/            # 核心功能模块
│   │   │   ├── __init__.py
│   │   │   ├── config.py
│   │   │   ├── dependencies.py
│   │   │   └── security.py
│   │   ├── db/              # 数据库相关
│   │   │   ├── __init__.py
│   │   │   └── database.py
│   │   ├── models/          # SQLAlchemy 数据模型
│   │   │   ├── __init__.py
│   │   │   ├── client.py
│   │   │   └── user.py
│   │   ├── schemas/         # Pydantic 模型/schemas
│   │   │   ├── __init__.py
│   │   │   ├── client.py
│   │   │   └── user.py
│   │   ├── utils/           # 后端工具函数
│   │   │   └── __init__.py
│   │   ├── __init__.py
│   │   └── main.py
│   ├── migrations/          # 数据库迁移脚本
│   │   ├── versions/
│   │   ├── README
│   │   ├── env.py
│   │   └── script.py.mako
│   ├── __init__.py
│   ├── create_tables.sql
│   ├── drop_tables.sql
│   ├── init_db.py
│   ├── main.py
│   └── requirements.txt
├── frontend/                # 前端代码 (Vue 3 + Vite)
│   ├── public/              # 静态资源目录
│   │   └── logo.png
│   ├── src/                 # 源代码目录
│   │   ├── api/
│   │   │   ├── account.js
│   │   │   ├── auth.js
│   │   │   ├── client.js
│   │   │   ├── dashboard.js
│   │   │   └── programs.js
│   │   ├── assets/
│   │   │   ├── logo.svg
│   │   │   └── vue.svg
│   │   ├── components/      # 可复用的 Vue 组件
│   │   │   ├── common/
│   │   │   │   ├── AnimatedInput.vue
│   │   │   │   └── Breadcrumb.vue
│   │   │   └── layout/
│   │   │       ├── Header.vue
│   │   │       ├── MainLayout.vue
│   │   │       └── Sidebar.vue
│   │   ├── router/          # 路由配置
│   │   │   ├── index.js
│   │   │   └── modules/
│   │   │       ├── account.js
│   │   │       ├── ai-tools.js
│   │   │       ├── auth.js
│   │   │       ├── clients.js
│   │   │       ├── dashboard.js
│   │   │       ├── error.js
│   │   │       ├── programs.js
│   │   │       ├── school.js
│   │   │       └── writing.js
│   │   ├── stores/          # 状态管理 (Pinia)
│   │   │   ├── auth.js
│   │   │   ├── clients.js
│   │   │   └── user.js
│   │   ├── styles/
│   │   │   └── index.css
│   │   ├── utils/           # 工具函数
│   │   │   ├── adapter.js
│   │   │   ├── auth.js
│   │   │   ├── format.js
│   │   │   └── request.js
│   │   ├── views/           # 页面级组件
│   │   │   ├── account/
│   │   │   │   ├── AccountSettings.vue
│   │   │   │   ├── Settings.vue
│   │   │   │   └── SubAccounts.vue
│   │   │   ├── ai-tools/
│   │   │   │   ├── AIDetector.vue
│   │   │   │   └── AIReducer.vue
│   │   │   ├── auth/
│   │   │   │   ├── ForgotPassword.vue
│   │   │   │   ├── Login.vue
│   │   │   │   └── Register.vue
│   │   │   ├── clients/
│   │   │   │   ├── ClientList.vue
│   │   │   │   └── ClientProfile.vue
│   │   │   ├── dashboard/
│   │   │   │   └── Dashboard.vue
│   │   │   ├── error/
│   │   │   │   └── NotFound.vue
│   │   │   ├── planning/
│   │   │   │   └── SchoolPlanning.vue
│   │   │   ├── programs/
│   │   │   │   └── ProgramDatabase.vue
│   │   │   ├── school/
│   │   │   │   └── SchoolAssistant.vue
│   │   │   └── writing/
│   │   │       ├── CV.vue
│   │   │       ├── PS.vue
│   │   │       └── RL.vue
│   │   ├── App.vue
│   │   ├── main.js
│   │   └── style.css
│   ├── auto-imports.d.ts
│   ├── components.d.ts
│   ├── index.html
│   ├── package.json
│   ├── postcss.config.cjs
│   ├── tailwind.config.js
│   └── vite.config.js
├── .cursor/
├── .gitignore
├── .prettierrc
├── README.md
├── start.sh
├── structure.md
├── tsconfig.json
└── tsconfig.node.json
```

## 🔧 安装和运行

### 系统要求
- Node.js 16+
- Python 3.9+
- PostgreSQL 12+

### 快速开始

1. **克隆项目**
```bash
git clone <repository-url>
cd TunshuEdu
```

2. **数据库设置**
```bash
# 创建数据库
createdb -U postgres tunshuedu_db

# 导入数据表结构
psql -U postgres -d tunshuedu_db -f backend/create_tables.sql
```

3. **后端启动**
```bash
cd backend
python -m venv .venv
source .venv/bin/activate  # Linux/macOS
# 或 .venv\Scripts\activate  # Windows

pip install -r requirements.txt
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

4. **前端启动**
```bash
cd frontend
npm install
npm run dev
```

5. **使用启动脚本（推荐）**
```bash
chmod +x start.sh
./start.sh
```

### 访问地址
- 前端应用: http://localhost:3000
- 后端API: http://localhost:8000
- API文档: http://localhost:8000/docs

### 默认账户
- 用户名: `admin`
- 密码: `admin123`

## 🔧 配置说明

### 后端配置
编辑 `backend/app/core/config.py`:
```python
# 数据库配置
POSTGRES_USER = "postgres"
POSTGRES_PASSWORD = "your_password"
POSTGRES_HOST = "localhost"
POSTGRES_DB = "tunshuedu_db"

# JWT配置
SECRET_KEY = "your-secret-key"
ACCESS_TOKEN_EXPIRE_MINUTES = 30
```

### 前端配置
创建 `frontend/.env.development`:
```env
VITE_API_URL=http://localhost:8000
```

## 🔥 重要特性

### AI选校系统
- **智能匹配**: 基于学生背景和历史案例的智能院校推荐
- **多维评分**: 院校层级、专业匹配、经历契合度等多维度评分
- **案例对比**: 与历史申请案例进行对比分析
- **实时筛选**: 支持多条件组合筛选

### 客户管理
- **全生命周期管理**: 从初次咨询到申请完成的完整流程
- **结构化信息**: 教育背景、工作经历、语言成绩等结构化存储
- **自定义模块**: 支持自定义背景模块和想法模块
- **档案归档**: 服务完成后的档案归档管理

### 文书系统
- **AI辅助写作**: 基于用户背景的个性化文书生成
- **智能优化**: 文书结构和内容的智能优化建议
- **模板管理**: 多种文书模板和格式支持
- **版本控制**: 文书修改历史和版本管理

## 📊 API文档

项目提供完整的API文档，启动后端服务后访问:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

主要API端点：
- `/api/auth/*` - 用户认证相关
- `/api/clients/*` - 客户管理相关  
- `/api/ai-selection/*` - AI选校相关
- `/api/dashboard` - 仪表板数据

## 🧪 开发指南

### 数据库迁移
```bash
cd backend
# 创建迁移
alembic revision --autogenerate -m "描述"
# 应用迁移
alembic upgrade head
```

### 代码规范
- 后端遵循 FastAPI 最佳实践
- 前端使用 Vue 3 组合式API
- 统一使用 TypeScript 类型检查
- 遵循 RESTful API 设计原则

### 测试
```bash
# 后端测试
cd backend
pytest

# 前端测试
cd frontend
npm run test
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送到分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目维护: 囤鼠科技团队
- 邮箱: <EMAIL>
- 官网: https://tunshuedu.com

---

**TunshuEdu** - 让留学申请更智能，让教育服务更高效 🎓✨