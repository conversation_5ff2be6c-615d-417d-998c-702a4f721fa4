import os
from pydantic_settings import BaseSettings
from typing import Optional
from pathlib import Path

# 获取当前文件所在目录
BASE_DIR = Path(__file__).resolve().parent.parent.parent

class Settings(BaseSettings):
    """
    应用配置类，使用 pydantic 进行环境变量和配置管理
    """
    # 项目基本信息
    PROJECT_NAME: str = "囤鼠科技教育平台"
    PROJECT_VERSION: str = "0.4.1"

    # 默认数据库配置
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "admin123"
    POSTGRES_HOST: str = "localhost"
    POSTGRES_PORT: str = "5432"
    POSTGRES_DB: str = "tunshuedu_db"
    DATABASE_URL: Optional[str] = None

    # 默认 JWT 配置
    SECRET_KEY: str = "your-secret-key-here-replace-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    class Config:
        """
        配置类的设置
        """
        case_sensitive = True
        env_file = str(BASE_DIR / ".env")
        env_file_encoding = "utf-8"

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        # 如果没有设置 DATABASE_URL，则根据其他数据库配置生成
        if not self.DATABASE_URL:
            # 使用标准连接URL，时区设置将在engine创建时通过connect_args添加
            self.DATABASE_URL = f"postgresql+asyncpg://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"

# 创建设置实例
settings = Settings()