from typing import Annotated, Optional
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jose import jwt, JWTError
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import ValidationError
from datetime import datetime

from app.db.database import get_db
from app.models.user import User
from app.schemas.user import TokenPayload
from app.core.config import settings

# OAuth2 密码令牌URL
oauth2_scheme = OAuth2PasswordBearer(
    tokenUrl="/api/auth/login"
)

# 定义依赖类型
DBSession = Annotated[AsyncSession, Depends(get_db)]
TokenDep = Annotated[str, Depends(oauth2_scheme)]

async def get_current_user(
    db: DBSession,
    token: TokenDep
) -> User:
    """
    获取当前用户
    
    Args:
        db: 数据库会话
        token: JWT 令牌
        
    Returns:
        User: 当前用户对象
        
    Raises:
        HTTPException: 凭证无效或用户不存在
    """
    try:
        # 解码 JWT 令牌
        payload = jwt.decode(
            token, 
            settings.SECRET_KEY, 
            algorithms=[settings.ALGORITHM]
        )
        token_data = TokenPayload(**payload)
        
        # 检查令牌是否过期
        if token_data.exp is None or datetime.fromtimestamp(token_data.exp) < datetime.now():
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="令牌已过期",
                headers={"WWW-Authenticate": "Bearer"},
            )
    except (JWTError, ValidationError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无法验证凭据",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 检查 sub 是否存在
    if token_data.sub is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 从数据库获取用户
    result = await db.execute(select(User).where(User.id == token_data.sub))
    user = result.scalars().first()
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="用户不存在"
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN, 
            detail="账户已被禁用"
        )
    
    return user

# 添加依赖类型注解
CurrentUser = Annotated[User, Depends(get_current_user)] 