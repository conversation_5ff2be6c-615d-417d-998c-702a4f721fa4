from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func
from typing import List, Dict, Any

from app.db.database import get_db
from app.models.user import User
from app.core.dependencies import CurrentUser, DBSession

# 创建路由器
router = APIRouter(prefix="/dashboard", tags=["仪表盘"])

@router.get("/", status_code=status.HTTP_200_OK)
async def get_dashboard_data(current_user: CurrentUser, db: DBSession):
    """
    获取仪表盘数据
    
    Args:
        current_user: 当前用户
        db: 数据库会话
        
    Returns:
        Dict: 仪表盘数据
    """
    # 获取用户总数
    result = await db.execute(select(func.count()).select_from(User))
    total_users = result.scalar()
    
    # 获取活跃用户数
    result = await db.execute(select(func.count()).select_from(User).where(User.is_active == True))
    active_users = result.scalar()
    
    # TODO: 实现获取最近活动的逻辑
    recent_activities = []
    
    return {
        "message": "获取仪表盘数据成功",
        "data": {
            "total_users": total_users,
            "active_users": active_users,
            "recent_activities": recent_activities
        }
    } 