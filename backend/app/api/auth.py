from fastapi import APIRouter, HTTPException, status, Depends
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from datetime import datetime

from app.core.security import create_access_token, create_refresh_token
from app.db.database import get_db
from app.models.user import User
from app.schemas.user import UserCreate, UserLogin, Token, UserResponse
from app.core.dependencies import CurrentUser, DBSession

# 创建路由器
router = APIRouter(prefix="/auth", tags=["认证"])

@router.post("/test", status_code=status.HTTP_200_OK)
async def test():
    """
    测试 API 是否正常运行
    """
    return {
        "message": "API is working!",
        "status": "success"
    }

@router.post("/register", response_model=Token, status_code=status.HTTP_201_CREATED)
async def register(user_in: UserCreate, db: DBSession):
    """
    用户注册
    
    Args:
        user_in: 注册用户信息
        db: 数据库会话
        
    Returns:
        Token: 包含访问令牌和刷新令牌的响应
        
    Raises:
        HTTPException: 用户名或邮箱已存在
    """
    # 检查用户名是否已存在
    result = await db.execute(select(User).where(User.username == user_in.username))
    if result.scalars().first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="用户名已存在"
        )
    
    # 检查邮箱是否已存在
    result = await db.execute(select(User).where(User.email == user_in.email))
    if result.scalars().first():
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="邮箱已存在"
        )
    
    # 创建新用户
    user = User(
        username=user_in.username,
        email=user_in.email
    )
    user.set_password(user_in.password)
    
    # 保存到数据库
    db.add(user)
    await db.commit()
    await db.refresh(user)
    
    # 生成访问令牌和刷新令牌
    access_token = create_access_token(subject=user.id)
    refresh_token = create_refresh_token(subject=user.id)
    
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer"
    }

@router.post("/login", response_model=Token)
async def login(db: DBSession, form_data: OAuth2PasswordRequestForm = Depends()):
    """
    用户登录
    
    Args:
        db: 数据库会话
        form_data: 表单数据，包含用户名和密码
        
    Returns:
        Token: 包含访问令牌和刷新令牌的响应
        
    Raises:
        HTTPException: 用户名或密码错误
    """
    # 查找用户
    result = await db.execute(select(User).where(User.username == form_data.username))
    user = result.scalars().first()
    
    # 检查用户是否存在以及密码是否正确
    if not user or not user.check_password(form_data.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="用户名或密码错误",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # 检查用户是否被禁用
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="账户已被禁用"
        )
    
    # 更新最后登录时间
    user.last_login = datetime.utcnow()
    await db.commit()
    
    # 生成访问令牌和刷新令牌
    access_token = create_access_token(subject=user.id)
    refresh_token = create_refresh_token(subject=user.id)
    
    # 返回token和用户信息
    return {
        "access_token": access_token,
        "refresh_token": refresh_token,
        "token_type": "bearer",
        "user": {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "nickname": user.nickname,
            "role": user.role,
            "is_active": user.is_active,
            "last_login": user.last_login.isoformat() if user.last_login else None,
            "created_at": user.created_at.isoformat() if user.created_at else None,
            "updated_at": user.updated_at.isoformat() if user.updated_at else None
        }
    }

@router.post("/refresh", response_model=Token)
async def refresh_token(db: DBSession, current_user: CurrentUser):
    """
    刷新令牌
    
    Args:
        db: 数据库会话
        current_user: 当前用户
        
    Returns:
        Token: 包含新的访问令牌的响应
    """
    # 生成新的访问令牌
    access_token = create_access_token(subject=current_user.id)
    
    return {
        "access_token": access_token,
        "refresh_token": create_refresh_token(subject=current_user.id),
        "token_type": "bearer"
    }

@router.get("/me", response_model=UserResponse)
async def get_current_user(current_user: CurrentUser):
    """
    获取当前用户信息
    
    Args:
        current_user: 当前用户
        
    Returns:
        UserResponse: 用户信息
    """
    return current_user

@router.put("/update-profile", response_model=UserResponse)
async def update_profile(current_user: CurrentUser, db: DBSession, data: dict):
    """
    更新用户个人资料
    
    Args:
        current_user: 当前用户
        db: 数据库会话
        data: 更新数据，目前支持更新昵称
        
    Returns:
        UserResponse: 更新后的用户信息
    """
    # 更新昵称
    if "nickname" in data:
        current_user.nickname = data["nickname"]
    
    # 更新最后修改时间
    current_user.updated_at = datetime.utcnow()
    
    # 保存到数据库
    db.add(current_user)
    await db.commit()
    await db.refresh(current_user)
    
    return current_user

@router.post("/change-password", status_code=status.HTTP_200_OK)
async def change_password(
    current_user: CurrentUser, 
    db: DBSession, 
    data: dict
):
    """
    修改密码
    
    Args:
        current_user: 当前用户
        db: 数据库会话
        data: 密码数据，包含old_password和new_password
        
    Returns:
        dict: 操作结果
        
    Raises:
        HTTPException: 旧密码错误或其他错误
    """
    # 验证参数
    if "old_password" not in data or "new_password" not in data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="缺少必要参数"
        )
    
    # 验证旧密码
    if not current_user.check_password(data["old_password"]):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="当前密码错误"
        )
    
    # 设置新密码
    current_user.set_password(data["new_password"])
    current_user.updated_at = datetime.utcnow()
    
    # 保存到数据库
    db.add(current_user)
    await db.commit()
    
    return {"message": "密码修改成功"} 