from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, List, Dict, Any
from datetime import datetime

# 基础模型
class BaseSchema(BaseModel):
    """
    基础Schema，包含共用的配置
    """
    class Config:
        """
        ORM 模式配置，允许直接从 ORM 模型创建
        """
        from_attributes = True

# 客户相关Schema
class ClientBase(BaseSchema):
    """
    客户基础信息
    """
    name: str = Field(..., description="客户姓名")
    gender: Optional[str] = Field(None, description="性别：male(男)、female(女)、unknown(未知)")
    phone: Optional[str] = Field(None, description="电话号码")
    email: Optional[str] = Field(None, description="电子邮箱")
    location: Optional[str] = Field(None, description="所在城市")
    address: Optional[str] = Field(None, description="详细地址")
    id_card: Optional[str] = Field(None, description="身份证号码")
    passport: Optional[str] = Field(None, description="护照号码")
    id_card_issuer: Optional[str] = Field(None, description="身份证签发机构")
    id_card_validity: Optional[str] = Field(None, description="身份证有效期")
    passport_issue_place: Optional[str] = Field(None, description="护照签发地")
    passport_issue_date: Optional[str] = Field(None, description="护照签发日期")
    passport_expiry: Optional[str] = Field(None, description="护照过期日期")
    service_type: Optional[str] = Field("undergraduate", description="服务类型：undergraduate(本科)、master(硕士)等")
    is_archived: Optional[bool] = Field(False, description="是否已归档（服务完成）")

class ClientCreate(ClientBase):
    """
    创建客户请求
    """
    pass

class ClientUpdate(ClientBase):
    """
    更新客户请求
    """
    name: Optional[str] = Field(None, description="客户姓名")

class ClientResponse(ClientBase):
    """
    客户响应
    """
    # id: int  
    id_hashed: str = Field(..., description="客户哈希ID") # 使用哈希ID代替真实ID
    user_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime

# 教育经历相关Schema
class EducationBase(BaseSchema):
    """
    教育经历基础信息
    """
    school: str = Field(..., description="学校名称")
    major: Optional[str] = Field(None, description="专业")
    degree: Optional[str] = Field(None, description="学位")
    gpa: Optional[str] = Field(None, description="GPA成绩")
    start_date: Optional[str] = Field(None, description="开始日期")
    end_date: Optional[str] = Field(None, description="结束日期")
    description: Optional[str] = Field(None, description="描述信息")
    order: Optional[int] = Field(0, description="排序顺序")

class EducationCreate(EducationBase):
    """
    创建教育经历请求
    """
    pass

class EducationUpdate(EducationBase):
    """
    更新教育经历请求
    """
    school: Optional[str] = Field(None, description="学校名称")

class EducationResponse(EducationBase):
    """
    教育经历响应
    """
    id: int
    # client_id: int
    created_at: datetime
    updated_at: datetime

# 学术经历相关Schema
class AcademicBase(BaseSchema):
    """
    学术经历基础信息
    """
    title: str = Field(..., description="项目主题")
    type: Optional[str] = Field(None, description="项目类型：毕业设计、论文、科研项目、学科课程项目、大学生创业项目、其他等")
    date: Optional[str] = Field(None, description="研究日期")
    description: Optional[str] = Field(None, description="详细描述")
    order: Optional[int] = Field(0, description="排序顺序")

class AcademicCreate(AcademicBase):
    """
    创建学术经历请求
    """
    pass

class AcademicUpdate(AcademicBase):
    """
    更新学术经历请求
    """
    title: Optional[str] = Field(None, description="项目主题")

class AcademicResponse(AcademicBase):
    """
    学术经历响应
    """
    id: int
    # client_id: int
    created_at: datetime
    updated_at: datetime

# 工作经历相关Schema
class WorkBase(BaseSchema):
    """
    工作经历基础信息
    """
    company: str = Field(..., description="公司/单位名称")
    position: Optional[str] = Field(None, description="职位")
    start_date: Optional[str] = Field(None, description="开始日期")
    end_date: Optional[str] = Field(None, description="结束日期")
    description: Optional[str] = Field(None, description="工作描述")
    order: Optional[int] = Field(0, description="排序顺序")

class WorkCreate(WorkBase):
    """
    创建工作经历请求
    """
    pass

class WorkUpdate(WorkBase):
    """
    更新工作经历请求
    """
    company: Optional[str] = Field(None, description="公司/单位名称")

class WorkResponse(WorkBase):
    """
    工作经历响应
    """
    id: int
    # client_id: int
    created_at: datetime
    updated_at: datetime

# 课外活动相关Schema
class ActivityBase(BaseSchema):
    """
    课外活动基础信息
    """
    name: str = Field(..., description="活动名称")
    role: Optional[str] = Field(None, description="角色/职位")
    start_date: Optional[str] = Field(None, description="开始日期")
    end_date: Optional[str] = Field(None, description="结束日期")
    description: Optional[str] = Field(None, description="活动描述")
    order: Optional[int] = Field(0, description="排序顺序")

class ActivityCreate(ActivityBase):
    """
    创建课外活动请求
    """
    pass

class ActivityUpdate(ActivityBase):
    """
    更新课外活动请求
    """
    name: Optional[str] = Field(None, description="活动名称")

class ActivityResponse(ActivityBase):
    """
    课外活动响应
    """
    id: int
    # client_id: int
    created_at: datetime
    updated_at: datetime

# 奖项荣誉相关Schema
class AwardBase(BaseSchema):
    """
    奖项荣誉基础信息
    """
    name: str = Field(..., description="奖项名称")
    level: Optional[str] = Field(None, description="奖项级别")
    date: Optional[str] = Field(None, description="获奖日期")
    description: Optional[str] = Field(None, description="奖项描述")
    order: Optional[int] = Field(0, description="排序顺序")

class AwardCreate(AwardBase):
    """
    创建奖项荣誉请求
    """
    pass

class AwardUpdate(AwardBase):
    """
    更新奖项荣誉请求
    """
    name: Optional[str] = Field(None, description="奖项名称")

class AwardResponse(AwardBase):
    """
    奖项荣誉响应
    """
    id: int
    # client_id: int
    created_at: datetime
    updated_at: datetime

# 技能相关Schema
class SkillBase(BaseSchema):
    """
    技能基础信息
    """
    type: str = Field(..., description="技能类型，如'专业技能'、'综合技能'等")
    description: Optional[str] = Field(None, description="技能描述，具体的技能内容")
    order: Optional[int] = Field(0, description="排序顺序")

class SkillCreate(SkillBase):
    """
    创建技能请求
    """
    pass

class SkillUpdate(SkillBase):
    """
    更新技能请求
    """
    type: Optional[str] = Field(None, description="技能类型")

class SkillResponse(SkillBase):
    """
    技能响应
    """
    id: int
    # client_id: int
    created_at: datetime
    updated_at: datetime

# 语言成绩相关Schema
class LanguageScoreBase(BaseSchema):
    """
    语言成绩基础信息
    """
    type: str = Field(..., description="考试类型：toefl、ielts、gre、gmat等")
    score: str = Field(..., description="分数")
    date: Optional[str] = Field(None, description="考试日期")
    validity: Optional[str] = Field(None, description="有效期")
    order: Optional[int] = Field(0, description="排序顺序")

class LanguageScoreCreate(LanguageScoreBase):
    """
    创建语言成绩请求
    """
    pass

class LanguageScoreUpdate(LanguageScoreBase):
    """
    更新语言成绩请求
    """
    type: Optional[str] = Field(None, description="考试类型")
    score: Optional[str] = Field(None, description="分数")

class LanguageScoreResponse(LanguageScoreBase):
    """
    语言成绩响应
    """
    id: int
    # client_id: int
    created_at: datetime
    updated_at: datetime

# 个人想法相关Schema
class ThoughtBase(BaseSchema):
    """
    个人想法基础信息
    """
    target_major: Optional[str] = Field(None, description="目标专业申请动机")
    personal_understanding: Optional[str] = Field(None, description="专业个人解读")
    academic_match: Optional[str] = Field(None, description="学术经历匹配")
    work_match: Optional[str] = Field(None, description="工作经历匹配")
    future_plan: Optional[str] = Field(None, description="未来规划")

class ThoughtCreate(ThoughtBase):
    """
    创建个人想法请求
    """
    pass

class ThoughtUpdate(ThoughtBase):
    """
    更新个人想法请求
    """
    pass

class ThoughtResponse(ThoughtBase):
    """
    个人想法响应
    """
    id: int
    # client_id: int
    created_at: datetime
    updated_at: datetime

# 自定义模块相关Schema
class CustomModuleBase(BaseSchema):
    """
    自定义模块基础信息
    """
    title: str = Field(..., description="模块标题")
    content: Optional[str] = Field(None, description="模块内容")
    order: Optional[int] = Field(0, description="排序顺序")

class CustomModuleCreate(CustomModuleBase):
    """
    创建自定义模块请求
    """
    pass

class CustomModuleUpdate(CustomModuleBase):
    """
    更新自定义模块请求
    """
    title: Optional[str] = Field(None, description="模块标题")
    content: Optional[str] = Field(None, description="模块内容")
    order: Optional[int] = Field(None, description="排序顺序")

class CustomModuleResponse(CustomModuleBase):
    """
    自定义模块响应
    """
    id: int
    # client_id: int
    created_at: datetime
    updated_at: datetime

# 完整客户信息响应
class ClientDetailResponse(ClientResponse):
    """
    客户详细信息响应，包含所有关联数据
    """
    education: List[EducationResponse] = []
    academic: List[AcademicResponse] = []
    work: List[WorkResponse] = []
    activities: List[ActivityResponse] = []
    awards: List[AwardResponse] = []
    skills: List[SkillResponse] = []
    language_scores: List[LanguageScoreResponse] = []
    thoughts: List[ThoughtResponse] = []
    background_modules: List[CustomModuleResponse] = []
    thought_modules: List[CustomModuleResponse] = []
