from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any

class UserInput(BaseModel):
    """用户输入信息模型"""
    undergraduate_school: str = Field(..., description="本科院校名称")
    undergraduate_school_tier: str = Field(..., description="本科院校层级，如tier1(985), tier2(211), tier3(普通一本)等")
    undergraduate_major: str = Field(..., description="本科专业")
    gpa: float = Field(..., description="GPA或均分，百分制")
    target_regions: List[str] = Field(..., description="目标留学地区，如['英国', '香港', '新加坡']")
    target_degree: str = Field(..., description="目标申请学位，如'硕士'")
    target_major_direction: str = Field(..., description="目标专业方向")
    
    # 可选字段
    key_experiences: Optional[str] = Field(None, description="关键经历描述，包括科研、实习、项目、奖项等")

class EnhancedUserProfile(BaseModel):
    """增强后的用户画像模型"""
    # 基础信息（从UserInput继承）
    undergraduate_school: str
    undergraduate_school_tier: str
    undergraduate_major: str
    gpa: float
    target_regions: List[str]
    target_degree: str
    target_major_direction: str
    key_experiences: Optional[str] = None
    
    # 新增字段，用于硬性筛选
    duration: Optional[str] = Field(None, description="期望项目时长")
    ranking_preference: Optional[List[str]] = Field(None, description="院校排名偏好")
    
    # 偏好类型（新增）
    preference_type: Optional[str] = Field("balanced", description="申请偏好类型: ranking_focused, major_focused, balanced")
    
    # AI智能选校开关（新增）
    enable_ai_selection: Optional[bool] = Field(True, description="是否启用AI智能选校，默认开启。关闭时只进行数据库硬筛选")
    
    # 增强信息（由系统分析生成）
    extracted_skills: List[str] = Field([], description="从经历中提取的技能和经验标签")
    inferred_interest_domain: Optional[str] = Field(None, description="推断的主要兴趣领域")
    academic_potential: Optional[str] = Field(None, description="学术潜力评估")
    
    class Config:
        arbitrary_types_allowed = True 