from pydantic import BaseModel, Field
from typing import List, Optional

class AcademicProfile(BaseModel):
    """学术背景"""
    education: str = Field(..., description="目标申请学历", examples=["high_school", "undergraduate", "master"])
    school: str = Field(..., description="就读/毕业院校")
    major: str = Field(..., description="专业")
    gpa: str = Field(..., description="GPA/平均分")
    gpaScale: str = Field(..., description="GPA制式/总分", examples=["4.0", "5.0", "100"])
    ranking: Optional[str] = Field(None, description="班级排名", examples=["5%", "10%", "20%"])

class IntentionProfile(BaseModel):
    """留学意向"""
    countries: List[str] = Field(..., description="意向国家/地区")
    majors: List[str] = Field(..., description="意向专业/领域")
    duration: Optional[str] = Field(None, description="期望项目时长")

class StrengthProfile(BaseModel):
    """软实力与偏好"""
    competition: Optional[List[str]] = Field([], description="竞赛经历")
    internship: Optional[List[str]] = Field([], description="实习经历")
    research: Optional[List[str]] = Field([], description="科研经历")
    rankingPreference: Optional[List[str]] = Field([], description="院校排名偏好")
    preference_type: Optional[str] = Field("balanced", description="申请偏好类型", examples=["ranking_focused", "major_focused", "balanced"])
    enable_ai_selection: Optional[bool] = Field(True, description="是否启用AI智能选校，默认开启。关闭时只进行数据库硬筛选")

class StudentProfile(BaseModel):
    """学生完整画像，作为API输入"""
    academic: AcademicProfile
    intention: IntentionProfile
    strength: StrengthProfile

    class Config:
        json_schema_extra = {
            "example": {
                "academic": {
                    "education": "master",
                    "school": "华南理工大学",
                    "major": "微电子科学与工程",
                    "gpa": "85.5",
                    "gpaScale": "100",
                    "ranking": "10%"
                },
                "intention": {
                    "countries": ["Hong Kong", "Singapore"],
                    "majors": ["computer_science", "electrical_engineering"],
                    "duration": "1"
                },
                "strength": {
                    "competition": [],
                    "internship": ["internet"],
                    "research": [],
                    "rankingPreference": ["QS-50", "QS-100"],
                    "preference_type": "balanced",
                    "enable_ai_selection": True
                }
            }
        } 