from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse
from typing import List, Dict, Any, AsyncGenerator
import json
import asyncio
import time

from app.ai_selection.schemas.student_profile import StudentProfile
from app.ai_selection.core.user_profile import build_user_profile
from app.ai_selection.core.candidate_pool import generate_candidate_pool, generate_hard_filtered_results
from app.ai_selection.core.school_matching import identify_reachable_schools, calculate_school_match_scores, determine_dynamic_qs_range, calculate_school_ranking_scores
from app.ai_selection.core.program_matching import match_programs_with_user_background
from app.ai_selection.core.streaming_ranking import stream_recommendations, stream_hard_filtered_results

router = APIRouter()

@router.post("/recommend/stream")
async def get_school_recommendations_stream(student_profile: StudentProfile):
    """
    流式推荐接口 - 实时返回推荐结果，改善用户体验
    
    Args:
        student_profile: 结构化的学生画像信息
        
    Returns:
        Server-Sent Events 流式响应
    """
    
    async def generate_recommendation_stream() -> AsyncGenerator[str, None]:
        """生成流式推荐数据"""
        try:
            # 发送开始事件
            yield f"data: {json.dumps({'type': 'start', 'message': '开始分析您的背景...'})}\n\n"
            
            # 阶段1: 用户画像构建
            start_time = time.time()
            enhanced_profile = await build_user_profile(student_profile)
            
            yield f"data: {json.dumps({'type': 'progress', 'stage': 'profile', 'message': '用户画像构建完成', 'elapsed': round(time.time() - start_time, 2)})}\n\n"
            
            # 检查是否启用AI智能选校
            enable_ai_selection = enhanced_profile.enable_ai_selection
            
            if not enable_ai_selection:
                # 硬筛选模式：只进行数据库筛选
                yield f"data: {json.dumps({'type': 'mode_selection', 'mode': 'hard_filter', 'message': '硬筛选模式：根据您的留学意向进行数据库筛选...'})}\n\n"
                
                # 阶段2: 动态确定QS排名范围（如果需要）
                stage_start = time.time()
                reachable_schools, similar_cases = await identify_reachable_schools(enhanced_profile)
                dynamic_qs_range = determine_dynamic_qs_range(
                    enhanced_profile.undergraduate_school_tier,
                    reachable_schools
                )
                min_r, max_r = dynamic_qs_range
                yield f"data: {json.dumps({'type': 'progress', 'stage': 'dynamic_range', 'message': f'已根据您的背景确定动态推荐范围: QS {min_r}-{max_r}', 'elapsed': round(time.time() - stage_start, 2)})}\n\n"
                
                # 阶段3: 生成硬筛选结果
                stage_start = time.time()
                hard_filtered_results = await generate_hard_filtered_results(enhanced_profile, dynamic_qs_range)
                
                if not hard_filtered_results:
                    yield f"data: {json.dumps({'type': 'error', 'message': '未找到符合条件的专业，请尝试调整专业方向或目标地区'})}\n\n"
                    return
                
                yield f"data: {json.dumps({'type': 'progress', 'stage': 'hard_filter_complete', 'message': f'硬筛选完成，找到 {len(hard_filtered_results)} 个匹配专业', 'elapsed': round(time.time() - stage_start, 2)})}\n\n"
                
                # 阶段4: 流式返回硬筛选结果
                async for stream_data in stream_hard_filtered_results(hard_filtered_results, enhanced_profile):
                    yield f"data: {json.dumps(stream_data)}\n\n"
                
            else:
                # AI智能选校模式：完整的AI推荐流程
                yield f"data: {json.dumps({'type': 'mode_selection', 'mode': 'ai_selection', 'message': 'AI智能选校模式：开始深度分析和个性化推荐...'})}\n\n"
                
                # 阶段2.A: 识别可达院校 (提前执行)
                stage_start = time.time()
                reachable_schools, similar_cases = await identify_reachable_schools(enhanced_profile)
                yield f"data: {json.dumps({'type': 'progress', 'stage': 'school_matching', 'message': f'院校可达性分析完成，发现 {len(reachable_schools)} 所相关院校案例', 'elapsed': round(time.time() - stage_start, 2)})}\n\n"

                # 阶段2.B: 动态确定QS排名范围
                dynamic_qs_range = determine_dynamic_qs_range(
                    enhanced_profile.undergraduate_school_tier,
                    reachable_schools
                )
                min_r, max_r = dynamic_qs_range
                yield f"data: {json.dumps({'type': 'progress', 'stage': 'dynamic_range', 'message': f'已根据您的背景确定动态推荐范围: QS {min_r}-{max_r}', 'elapsed': round(time.time() - stage_start, 2)})}\n\n"

                # 阶段3: 候选池生成 (使用动态范围)
                stage_start = time.time()
                candidate_pool = await generate_candidate_pool(enhanced_profile, dynamic_qs_range)
                
                if not candidate_pool:
                    yield f"data: {json.dumps({'type': 'error', 'message': '未找到符合条件的专业，请尝试调整专业方向或目标地区'})}\n\n"
                    return
                
                yield f"data: {json.dumps({'type': 'progress', 'stage': 'candidates', 'message': f'找到 {len(candidate_pool)} 个候选专业', 'elapsed': round(time.time() - stage_start, 2)})}\n\n"
                
                # 阶段4: 计算院校匹配分数 (现在可以更准确)
                stage_start = time.time()
                candidate_pool = await calculate_school_match_scores(candidate_pool, reachable_schools)
                
                yield f"data: {json.dumps({'type': 'progress', 'stage': 'school_scoring', 'message': f'院校匹配分数计算完成', 'elapsed': round(time.time() - stage_start, 2)})}\n\n"
                
                # 阶段5: 流式专业匹配和推荐生成
                async for stream_data in stream_recommendations(candidate_pool, enhanced_profile, similar_cases):
                    yield f"data: {json.dumps(stream_data)}\n\n"
            
            # 发送完成事件
            total_time = time.time() - start_time
            yield f"data: {json.dumps({'type': 'complete', 'message': '推荐生成完成', 'total_elapsed': round(total_time, 2)})}\n\n"
            
        except Exception as e:
            yield f"data: {json.dumps({'type': 'error', 'message': f'推荐过程中出现错误: {str(e)}'})}\n\n"
    
    return StreamingResponse(
        generate_recommendation_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )

@router.post("/analyze_profile")
async def analyze_user_profile(student_profile: StudentProfile) -> Dict[str, Any]:
    """
    分析用户画像，返回增强后的用户信息
    
    Args:
        student_profile: 结构化的学生画像信息
        
    Returns:
        增强后的用户画像
    """
    enhanced_profile = await build_user_profile(student_profile)
    return enhanced_profile.dict() 