from typing import List, Dict, Any
import asyncio

from app.ai_selection.schemas.student_profile import StudentProfile
from app.ai_selection.schemas.user import EnhancedUserProfile
from app.ai_selection.utils.adapter import (
    convert_gpa_to_100_scale, 
    map_country_names, 
    format_strength_as_text,
    infer_school_tier,
    map_major_names,
    map_degree_type
)
from app.ai_selection.utils.llm import enhance_user_profile_async

async def build_user_profile(student_profile: StudentProfile) -> EnhancedUserProfile:
    """
    通过适配器和LLM构建增强用户画像
    
    Args:
        student_profile: 前端传入的学生画像信息
        
    Returns:
        增强后的用户画像
    """
    # 步骤1: 使用适配器将StudentProfile转换为核心逻辑所需的字典格式
    
    # 1.1 学术背景处理
    academic = student_profile.academic
    gpa_100 = convert_gpa_to_100_scale(academic.gpa, academic.gpaScale)
    school_tier = infer_school_tier(academic.school)
    
    # 1.2 留学意向处理
    intention = student_profile.intention
    target_regions = map_country_names(intention.countries)
    target_major_direction = map_major_names(intention.majors)
    
    # 1.3 软实力与偏好处理
    strength = student_profile.strength
    key_experiences = format_strength_as_text(strength)
    
    # 1.4 偏好类型处理 - 直接从前端获取，不再自动推断
    preference_type = getattr(strength, 'preference_type', 'balanced')  # 默认为平衡型
    
    # 1.5 AI智能选校开关
    enable_ai_selection = getattr(strength, 'enable_ai_selection', True)  # 默认开启
    
    # 步骤2: 构建基础画像字典
    basic_profile = {
        "undergraduate_school": academic.school,
        "undergraduate_school_tier": school_tier,
        "undergraduate_major": academic.major,
        "gpa": gpa_100,
        "target_regions": target_regions,
        "target_degree": map_degree_type(academic.education),
        "target_major_direction": target_major_direction,
        "key_experiences": key_experiences,
        "duration": intention.duration,
        "ranking_preference": strength.rankingPreference,
        "preference_type": preference_type,  # 使用前端选择的偏好类型
        "enable_ai_selection": enable_ai_selection  # 使用前端选择的AI智能选校开关
    }
    
    # 步骤3: 根据是否启用AI选校，决定是否调用LLM进行画像增强
    if enable_ai_selection:
        # AI选校模式：调用LLM进行画像增强
        if key_experiences and key_experiences.strip():
            enhanced_profile = await enhance_user_profile_async(basic_profile, key_experiences)
        else:
            # 如果没有经历描述，也使用基础画像并补充默认值
            enhanced_profile = basic_profile.copy()
            enhanced_profile.update({
                "extracted_skills": [],
                "inferred_interest_domain": None,
                "academic_potential": "中等"
            })
    else:
        # 硬筛选模式：不调用LLM，直接使用基础画像并补充默认值
        enhanced_profile = basic_profile.copy()
        enhanced_profile.update({
            "extracted_skills": [],
            "inferred_interest_domain": None,
            "academic_potential": None  # 硬筛选模式下不评估潜力
        })
    
    # 步骤4: 返回EnhancedUserProfile对象
    return EnhancedUserProfile(**enhanced_profile) 