from typing import List, Dict, Any, Set, Optional, <PERSON>ple
import json
import numpy as np
import re
from collections import defaultdict
from sklearn.metrics.pairwise import cosine_similarity
from sqlalchemy import select, and_, or_, case, func, Float, Integer

from app.db.database import get_db
from app.ai_selection.db.models import (
    AISelectionProgram as Program, 
    AISelectionCase as Case
)
from app.ai_selection.schemas.user import EnhancedUserProfile
from app.ai_selection.config import (
    CANDIDATE_POOL_SIZE,
    VECTOR_SIMILARITY_THRESHOLD,
    MAX_PROGRAMS_PER_SCHOOL
)
from app.ai_selection.utils.rag import embed_text
from app.ai_selection.utils.ranking_parser import parse_qs_rank, get_ranking_sort_key, infer_school_tier_from_qs_rank

async def generate_candidate_pool(
    user_profile: EnhancedUserProfile,
    dynamic_qs_range: Optional[Tuple[Optional[int], Optional[int]]] = None
) -> List[Dict[str, Any]]:
    """
    基于用户画像生成初步候选池
    
    Args:
        user_profile: 增强用户画像
        dynamic_qs_range: 动态计算的QS排名筛选范围
        
    Returns:
        候选池列表，包含学校和专业信息
    """
    print(f"\n=== 开始生成候选池 ===")
    print(f"目标专业方向: {user_profile.target_major_direction}")
    print(f"目标地区: {user_profile.target_regions}")
    print(f"目标学位: {user_profile.target_degree}")
    
    # 首先使用ORM硬筛选生成候选池
    print(f"\n--- 第一阶段：数据库筛选 ---")
    candidate_pool = await _orm_based_filtering(user_profile, dynamic_qs_range)
    db_count = len(candidate_pool)
    print(f"数据库筛选结果: {db_count} 个专业")
    
    # 统计各类匹配的数量
    exact_matches = len([c for c in candidate_pool if c.get("match_source") == "exact_match"])
    fuzzy_matches = len([c for c in candidate_pool if c.get("match_source") == "fuzzy_match"])
    related_matches = len([c for c in candidate_pool if c.get("match_source") == "related_match"])
    
    print(f"  - 精确匹配: {exact_matches} 个")
    print(f"  - 模糊匹配: {fuzzy_matches} 个") 
    print(f"  - 相关匹配: {related_matches} 个")
    
    # 检查候选池大小，如果不足预期，则使用向量检索补充
    min_expected_size = max(CANDIDATE_POOL_SIZE, 10)  # 至少期望10个结果或配置的候选池大小
    
    vector_count = 0
    if len(candidate_pool) < min_expected_size:
        print(f"\n--- 第二阶段：向量检索补充 ---")
        print(f"当前候选数量({db_count})少于期望数量({min_expected_size})，启动向量检索补充")
        
        # 获取已有候选池的专业ID
        existing_program_ids = {program["id"] for program in candidate_pool}
        needed_count = min_expected_size - len(candidate_pool)
        
        # 使用向量检索补充候选池，使用配置的相似度阈值
        vector_results = await _vector_based_filtering(
            user_profile, 
            existing_program_ids, 
            needed_count,
            similarity_threshold=VECTOR_SIMILARITY_THRESHOLD
        )
        
        vector_count = len(vector_results)
        print(f"向量检索结果: {vector_count} 个专业")
        
        if vector_results:
            # 显示向量匹配的相似度分布
            similarities = [r.get("vector_similarity", 0) for r in vector_results]
            print(f"  - 平均相似度: {sum(similarities)/len(similarities):.3f}")
            print(f"  - 最高相似度: {max(similarities):.3f}")
            print(f"  - 最低相似度: {min(similarities):.3f}")
        
        # 合并结果
        candidate_pool.extend(vector_results)
    else:
        print(f"\n--- 跳过向量检索 ---")
        print(f"数据库筛选结果({db_count})已满足期望数量({min_expected_size})")
    
    total_count = len(candidate_pool)
    print(f"\n=== 候选池生成完成 ===")
    print(f"总候选数量: {total_count} 个专业")
    print(f"数据库筛选: {db_count} 个 ({db_count/max(total_count, 1)*100:.1f}%)")
    print(f"向量检索: {vector_count} 个 ({vector_count/max(total_count, 1)*100:.1f}%)")
    
    # 显示最终候选池的来源分布
    source_stats = {}
    for candidate in candidate_pool:
        source = candidate.get("match_source", "unknown")
        source_stats[source] = source_stats.get(source, 0) + 1
    
    print(f"\n详细来源分布:")
    for source, count in source_stats.items():
        print(f"  - {source}: {count} 个")
    
    return candidate_pool

async def _orm_based_filtering(
    user_profile: EnhancedUserProfile,
    dynamic_qs_range: Optional[Tuple[Optional[int], Optional[int]]] = None
) -> List[Dict[str, Any]]:
    """
    使用ORM硬筛选条件生成候选池，采用更精确的专业方向匹配策略
    
    Args:
        user_profile: 增强用户画像
        dynamic_qs_range: 动态QS排名范围
        
    Returns:
        ORM筛选的候选池
    """
    async for session in get_db():
        # 第一层：精确匹配策略
        exact_matches = await _get_exact_matches(
            session, user_profile, dynamic_qs_range
        )
        
        # 第二层：如果精确匹配结果不足，进行模糊匹配
        if len(exact_matches) < CANDIDATE_POOL_SIZE // 2:  # 少于期望结果的 一半
            fuzzy_matches = await _get_fuzzy_matches(
                session, user_profile, 
                {p["id"] for p in exact_matches},
                dynamic_qs_range
            )
            exact_matches.extend(fuzzy_matches)
        
        # 第三层：如果仍然不足，考虑相关专业匹配
        if len(exact_matches) < CANDIDATE_POOL_SIZE // 3:  # 少于期望结果的三分之一
            related_matches = await _get_related_matches(
                session, user_profile,
                {p["id"] for p in exact_matches},
                dynamic_qs_range
            )
            exact_matches.extend(related_matches)
        
        # 在所有匹配结束后，统一进行多样性控制
        diverse_matches = await _apply_school_diversity_limit(exact_matches, user_profile)
        
        return diverse_matches[:CANDIDATE_POOL_SIZE]

async def _get_exact_matches(
    session, user_profile: EnhancedUserProfile, 
    dynamic_qs_range: Optional[Tuple[Optional[int], Optional[int]]] = None
) -> List[Dict[str, Any]]:
    """
    第一层：精确匹配 - 专业名称中包含用户目标专业方向的关键词
    在专业方向匹配的前提下，按QS排名排序
    """
    # 从user_profile中提取参数
    target_regions = user_profile.target_regions
    target_degree = user_profile.target_degree
    target_major_direction = user_profile.target_major_direction.lower()
    duration = user_profile.duration

    # 提取目标专业方向的核心关键词
    core_keywords = _extract_core_keywords(target_major_direction)
    
    # 构建基础筛选条件
    base_conditions = [
        Program.degree == target_degree,
        Program.school_region.in_(target_regions),
    ]

    # 添加项目时长筛选条件
    if duration:
        try:
            duration_float = float(duration)
            duration_condition = None
            if duration_float >= 3: # "3年及以上"
                duration_condition = and_(
                    Program.program_duration.isnot(None),
                    Program.program_duration.op('~')(r'^[0-9\.]+'),
                    func.substring(Program.program_duration, r'^[0-9\.]+').cast(Float) >= duration_float
                )
            else: # 精确匹配
                duration_condition = and_(
                    Program.program_duration.isnot(None),
                    Program.program_duration.op('~')(r'^[0-9\.]+'),
                    func.substring(Program.program_duration, r'^[0-9\.]+').cast(Float) == duration_float
                )
            if duration_condition is not None:
                base_conditions.append(duration_condition)
        except (ValueError, TypeError):
            pass # 忽略无效的duration

    # 添加动态QS排名范围筛选条件
    if dynamic_qs_range:
        min_rank, max_rank = dynamic_qs_range
        rank_conditions = []
        if min_rank is not None:
            rank_conditions.append(func.substring(Program.school_qs_rank, r'^[0-9]+').cast(Integer) >= min_rank)
        if max_rank is not None:
            rank_conditions.append(func.substring(Program.school_qs_rank, r'^[0-9]+').cast(Integer) <= max_rank)
        
        if rank_conditions:
            base_conditions.append(
                and_(
                    Program.school_qs_rank.isnot(None),
                    Program.school_qs_rank.op('~')(r'^[0-9]'),
                    *rank_conditions
                )
            )

    # 构建精确专业匹配条件
    exact_major_conditions = []
    for keyword in core_keywords:
        exact_major_conditions.append(
            or_(
                Program.program_name_cn.like(f"%{keyword}%"),
                Program.program_name_en.ilike(f"%{keyword}%"),
                Program.program_direction.like(f"%{keyword}%")
            )
        )
    
    if not exact_major_conditions:
        return []

    query = (
        select(
            Program.id,
            Program.program_name_cn,
            Program.program_name_en,
            Program.program_direction,
            Program.faculty,
            Program.program_objectives,
            Program.degree,
            Program.program_duration,
            Program.program_tuition,
            Program.gpa_requirements,
            Program.school_name_cn,
            Program.school_name_en,
            Program.school_qs_rank,
            Program.school_region,
            Program.embedding
        )
        .where(
            and_(
                *base_conditions,
                or_(*exact_major_conditions)
            )
        )
    )
    
    result = await session.execute(query)
    rows = result.fetchall()
    
    # 转换为候选项目列表
    candidates = []
    for row in rows:
        candidate = _create_candidate_dict(row, "exact_match")
        candidates.append(candidate)
    
    # 按QS排名排序（Python端排序）
    candidates.sort(key=lambda x: get_ranking_sort_key(x.get("school_qs_rank", "")))
    
    return candidates

async def _get_fuzzy_matches(
    session, user_profile: EnhancedUserProfile, 
    exclude_ids: Set[int], dynamic_qs_range: Optional[Tuple[Optional[int], Optional[int]]] = None
) -> List[Dict[str, Any]]:
    """
    第二层：模糊匹配 - 基于学科分类和专业方向匹配
    在学科匹配的前提下，按QS排名排序
    """
    # 从user_profile中提取参数
    target_regions = user_profile.target_regions
    target_degree = user_profile.target_degree
    target_major_direction = user_profile.target_major_direction.lower()
    duration = user_profile.duration

    # 基于目标专业方向推断学科领域，使用更精确的映射
    discipline_keywords = _get_discipline_keywords(target_major_direction)
    
    if not discipline_keywords:
        return []
        
    # 构建基础筛选条件
    base_conditions = [
        Program.degree == target_degree,
        Program.school_region.in_(target_regions),
    ]
    if exclude_ids:
        base_conditions.append(~Program.id.in_(exclude_ids))

    # 添加项目时长筛选条件
    if duration:
        try:
            duration_float = float(duration)
            duration_condition = None
            if duration_float >= 3: # "3年及以上"
                duration_condition = and_(
                    Program.program_duration.isnot(None),
                    Program.program_duration.op('~')(r'^[0-9\.]+'),
                    func.substring(Program.program_duration, r'^[0-9\.]+').cast(Float) >= duration_float
                )
            else: # 精确匹配
                duration_condition = and_(
                    Program.program_duration.isnot(None),
                    Program.program_duration.op('~')(r'^[0-9\.]+'),
                    func.substring(Program.program_duration, r'^[0-9\.]+').cast(Float) == duration_float
                )
            if duration_condition is not None:
                base_conditions.append(duration_condition)
        except (ValueError, TypeError):
            pass

    # 添加动态QS排名范围筛选条件
    if dynamic_qs_range:
        min_rank, max_rank = dynamic_qs_range
        rank_conditions = []
        if min_rank is not None:
            rank_conditions.append(func.substring(Program.school_qs_rank, r'^[0-9]+').cast(Integer) >= min_rank)
        if max_rank is not None:
            rank_conditions.append(func.substring(Program.school_qs_rank, r'^[0-9]+').cast(Integer) <= max_rank)
        
        if rank_conditions:
            base_conditions.append(
                and_(
                    Program.school_qs_rank.isnot(None),
                    Program.school_qs_rank.op('~')(r'^[0-9]'),
                    *rank_conditions
                )
            )

    # 构建模糊专业匹配条件
    fuzzy_major_conditions = []
    for keyword in discipline_keywords:
        fuzzy_major_conditions.append(Program.program_direction.like(f"%{keyword}%"))
    
    if not fuzzy_major_conditions:
        return []
    
    query = (
        select(
            Program.id,
            Program.program_name_cn,
            Program.program_name_en,
            Program.program_direction,
            Program.faculty,
            Program.program_objectives,
            Program.degree,
            Program.program_duration,
            Program.program_tuition,
            Program.gpa_requirements,
            Program.school_name_cn,
            Program.school_name_en,
            Program.school_qs_rank,
            Program.school_region,
            Program.embedding
        )
        .where(
            and_(
                *base_conditions,
                or_(*fuzzy_major_conditions)
            )
        )
    )
    
    result = await session.execute(query)
    rows = result.fetchall()
    
    candidates = []
    for row in rows:
        candidate = _create_candidate_dict(row, "fuzzy_match")
        candidates.append(candidate)
    
    # 按QS排名排序
    candidates.sort(key=lambda x: get_ranking_sort_key(x.get("school_qs_rank", "")))
    
    return candidates

async def _get_related_matches(
    session, user_profile: EnhancedUserProfile,
    exclude_ids: Set[int],
    dynamic_qs_range: Optional[Tuple[Optional[int], Optional[int]]] = None,
) -> List[Dict[str, Any]]:
    """
    第三层：相关匹配 - 基于本科专业和推断兴趣领域的相关专业
    在相关专业匹配的前提下，按QS排名排序
    """
    # 从user_profile中提取参数
    target_regions = user_profile.target_regions
    target_degree = user_profile.target_degree
    undergraduate_major = user_profile.undergraduate_major.lower()
    inferred_interest_domain = user_profile.inferred_interest_domain
    duration = user_profile.duration

    # 获取与本科专业相关的专业方向
    related_disciplines = _get_related_disciplines(undergraduate_major, inferred_interest_domain)
    
    if not related_disciplines:
        return []

    # 构建基础筛选条件
    base_conditions = [
        Program.degree == target_degree,
        Program.school_region.in_(target_regions),
    ]
    if exclude_ids:
        base_conditions.append(~Program.id.in_(exclude_ids))

    # 添加项目时长筛选条件
    if duration:
        try:
            duration_float = float(duration)
            duration_condition = None
            if duration_float >= 3: # "3年及以上"
                duration_condition = and_(
                    Program.program_duration.isnot(None),
                    Program.program_duration.op('~')(r'^[0-9\.]+'),
                    func.substring(Program.program_duration, r'^[0-9\.]+').cast(Float) >= duration_float
                )
            else: # 精确匹配
                duration_condition = and_(
                    Program.program_duration.isnot(None),
                    Program.program_duration.op('~')(r'^[0-9\.]+'),
                    func.substring(Program.program_duration, r'^[0-9\.]+').cast(Float) == duration_float
                )
            if duration_condition is not None:
                base_conditions.append(duration_condition)
        except (ValueError, TypeError):
            pass

    # 添加动态QS排名范围筛选条件
    if dynamic_qs_range:
        min_rank, max_rank = dynamic_qs_range
        rank_conditions = []
        if min_rank is not None:
            rank_conditions.append(func.substring(Program.school_qs_rank, r'^[0-9]+').cast(Integer) >= min_rank)
        if max_rank is not None:
            rank_conditions.append(func.substring(Program.school_qs_rank, r'^[0-9]+').cast(Integer) <= max_rank)
        
        if rank_conditions:
            base_conditions.append(
                and_(
                    Program.school_qs_rank.isnot(None),
                    Program.school_qs_rank.op('~')(r'^[0-9]'),
                    *rank_conditions
                )
            )
    
    # 构建相关专业匹配条件
    related_major_conditions = []
    for discipline in related_disciplines:
        related_major_conditions.append(Program.program_direction.like(f"%{discipline}%"))
    
    if not related_major_conditions:
        return []

    query = (
        select(
            Program.id,
            Program.program_name_cn,
            Program.program_name_en,
            Program.program_direction,
            Program.faculty,
            Program.program_objectives,
            Program.degree,
            Program.program_duration,
            Program.program_tuition,
            Program.gpa_requirements,
            Program.school_name_cn,
            Program.school_name_en,
            Program.school_qs_rank,
            Program.school_region,
            Program.embedding
        )
        .where(
            and_(
                *base_conditions,
                or_(*related_major_conditions)
            )
        )
    )

    result = await session.execute(query)
    rows = result.fetchall()

    candidates = []
    for row in rows:
        candidate = _create_candidate_dict(row, "related_match")
        candidates.append(candidate)
    
    # 按QS排名排序
    candidates.sort(key=lambda x: get_ranking_sort_key(x.get("school_qs_rank", "")))
    
    return candidates

def _extract_core_keywords(target_major_direction: str) -> List[str]:
    """
    从目标专业方向中提取核心关键词
    """
    # 定义核心专业关键词映射
    keyword_mapping = {
        # 计算机与信息技术类
        "人工智能": ["人工智能", "AI", "artificial intelligence", "machine learning", "机器学习", "深度学习", "deep learning", "神经网络", "neural network"],
        "机器学习": ["机器学习", "machine learning", "AI", "人工智能", "数据科学", "统计学习", "pattern recognition", "模式识别"],
        "数据科学": ["数据科学", "data science", "大数据", "数据分析", "analytics", "数据挖掘", "data mining", "商业分析", "business analytics"],
        "计算机科学": ["计算机科学", "computer science", "计算机", "软件工程", "信息技术", "信息系统", "计算机技术"],
        "软件工程": ["软件工程", "software engineering", "软件开发", "程序设计", "软件设计", "系统开发", "编程"],
        "网络安全": ["网络安全", "cybersecurity", "信息安全", "网络工程", "数据安全", "security", "密码学", "cryptography"],
        "信息工程": ["信息工程", "information engineering", "信息技术", "信息系统", "信息管理", "信息科学"],
        
        # 工程技术类
        "电子工程": ["电子工程", "electronic engineering", "电子", "微电子", "集成电路", "半导体", "电子技术", "电路设计"],
        "电气工程": ["电气工程", "electrical engineering", "电气", "电力", "电力工程", "电力系统", "自动化", "控制工程"],
        "通信工程": ["通信工程", "communication", "通信", "信息工程", "电信", "telecommunications", "无线通信", "网络通信"],
        "机械工程": ["机械", "mechanical", "机械工程", "自动化", "制造工程", "机械设计", "工业工程", "机器人", "robotics"],
        "土木工程": ["土木", "civil engineering", "建筑工程", "结构工程", "基础设施", "交通工程", "水利工程", "环境工程"],
        "材料工程": ["材料", "materials", "材料科学", "材料工程", "纳米材料", "复合材料", "金属材料", "高分子材料"],
        "化学工程": ["化学工程", "chemical engineering", "化工", "工艺工程", "石油工程", "生物工程", "环境工程"],
        "航空航天": ["航空", "航天", "aerospace", "aeronautical", "飞行器", "空间技术", "导航", "卫星技术"],
        "生物医学工程": ["生物医学工程", "biomedical engineering", "医学工程", "生物工程", "医疗器械", "康复工程"],
        
        # 商科与经济类
        "金融": ["金融", "finance", "投资", "银行", "证券", "保险", "风险管理", "金融工程", "量化金融", "fintech"],
        "经济": ["经济", "economics", "经济学", "宏观经济", "微观经济", "计量经济", "国际经济", "发展经济学"],
        "管理": ["管理", "management", "工商管理", "企业管理", "MBA", "战略管理", "人力资源", "运营管理"],
        "会计": ["会计", "accounting", "财务", "审计", "税务", "财务管理", "成本会计", "管理会计"],
        "市场营销": ["市场营销", "marketing", "市场", "品牌管理", "数字营销", "消费者行为", "销售管理"],
        "国际贸易": ["国际贸易", "international trade", "贸易", "进出口", "供应链", "物流", "国际商务"],
        "商业分析": ["商业分析", "business analytics", "商业智能", "business intelligence", "数据分析", "运筹学"],
        
        # 理学类
        "数学": ["数学", "mathematics", "应用数学", "统计", "statistics", "数理统计", "运筹学", "概率论"],
        "物理": ["物理", "physics", "应用物理", "理论物理", "凝聚态物理", "量子物理", "光学", "核物理"],
        "化学": ["化学", "chemistry", "应用化学", "材料化学", "有机化学", "无机化学", "物理化学", "分析化学"],
        "生物": ["生物", "biology", "生物科学", "生物技术", "biotechnology", "分子生物学", "细胞生物学", "遗传学"],
        "环境科学": ["环境科学", "environmental science", "环境工程", "生态学", "环境保护", "可持续发展", "气候变化"],
        "地理": ["地理", "geography", "地理信息系统", "GIS", "遥感", "测绘", "地质", "地球科学"],
        
        # 医学与健康类
        "医学": ["医学", "medicine", "临床医学", "基础医学", "预防医学", "医疗", "健康", "生物医学"],
        "护理": ["护理", "nursing", "护理学", "健康护理", "临床护理", "社区护理"],
        "药学": ["药学", "pharmacy", "药物", "制药", "临床药学", "药物化学", "药剂学"],
        "公共卫生": ["公共卫生", "public health", "流行病学", "健康管理", "卫生政策", "全球健康"],
        
        # 人文社科类
        "心理学": ["心理学", "psychology", "心理", "认知科学", "行为科学", "社会心理学", "临床心理学"],
        "教育": ["教育", "education", "教育学", "师范", "教学", "课程设计", "教育技术", "教育管理"],
        "法学": ["法学", "law", "法律", "司法", "国际法", "商法", "知识产权法", "法律实务"],
        "社会学": ["社会学", "sociology", "社会工作", "社会政策", "人类学", "社会研究"],
        "政治学": ["政治学", "political science", "政治", "国际关系", "公共政策", "外交", "政府管理"],
        "新闻传播": ["新闻", "journalism", "传播", "媒体", "广告", "公关", "数字媒体", "影视制作"],
        "语言文学": ["中文", "英语", "语言学", "文学", "翻译", "对外汉语", "应用语言学"],
        
        # 艺术设计类
        "设计": ["设计", "design", "艺术设计", "工业设计", "平面设计", "建筑设计", "室内设计", "产品设计"],
        "建筑": ["建筑", "architecture", "建筑学", "城市规划", "景观设计", "建筑设计"],
        "艺术": ["艺术", "art", "美术", "音乐", "舞蹈", "戏剧", "电影", "创意产业"],
        
        # 农业与食品类
        "农业": ["农业", "agriculture", "农学", "园艺", "植物保护", "农业工程", "食品科学", "动物科学"],
        "食品": ["食品", "food", "食品科学", "营养学", "食品工程", "食品安全", "食品技术"]
    }
    
    keywords = []
    target_lower = target_major_direction.lower()
    
    # 直接匹配
    for key, values in keyword_mapping.items():
        if key in target_major_direction:
            keywords.extend(values)
            break
    
    # 如果没有直接匹配，尝试部分匹配
    if not keywords:
        for key, values in keyword_mapping.items():
            if any(part in target_major_direction for part in key.split()):
                keywords.extend(values[:2])  # 只取前两个最相关的关键词
    
    # 如果仍然没有匹配，返回原始输入的分词
    if not keywords:
        # 简单分词：按空格、顿号等分割
        parts = re.split(r'[、，,\s]+', target_major_direction)
        keywords = [part.strip() for part in parts if len(part.strip()) > 1]
    
    return list(set(keywords))  # 去重

def _get_discipline_keywords(target_major_direction: str) -> List[str]:
    """
    基于目标专业方向获取学科关键词
    """
    discipline_mapping = {
        # 计算机与信息技术类
        "计算机": ["计算机科学", "软件工程", "信息技术", "信息系统", "网络工程"],
        "人工智能": ["计算机科学", "机器学习", "数据科学", "人工智能", "智能科学"],
        "软件": ["计算机科学", "软件工程", "信息技术", "系统工程"],
        "数据": ["数据科学", "计算机科学", "统计学", "商业分析", "信息管理"],
        "网络": ["网络工程", "信息安全", "计算机科学", "通信工程"],
        "信息": ["信息技术", "信息系统", "信息管理", "信息科学", "计算机科学"],
        
        # 工程技术类
        "电子": ["电子工程", "电气工程", "通信工程", "微电子", "集成电路"],
        "电气": ["电气工程", "电子工程", "自动化", "控制工程", "电力工程"],
        "机械": ["机械工程", "自动化", "制造工程", "工业工程", "机器人工程"],
        "土木": ["土木工程", "建筑工程", "结构工程", "交通工程", "水利工程"],
        "材料": ["材料科学", "材料工程", "化学工程", "纳米科学"],
        "化工": ["化学工程", "化工工艺", "石油工程", "环境工程"],
        "航空": ["航空工程", "航天工程", "飞行器设计", "空间科学"],
        "生物工程": ["生物医学工程", "生物工程", "医学工程", "康复工程"],
        "环境": ["环境工程", "环境科学", "生态工程", "水处理工程"],
        
        # 理学类
        "数学": ["数学", "统计学", "应用数学", "计算数学", "运筹学"],
        "物理": ["物理学", "应用物理", "材料物理", "光学", "核物理"],
        "化学": ["化学", "材料化学", "应用化学", "化学生物学", "药物化学"],
        "生物": ["生物科学", "生物技术", "生物工程", "分子生物学", "生态学"],
        "地理": ["地理科学", "地理信息系统", "遥感科学", "测绘工程"],
        "地质": ["地质学", "地球科学", "地质工程", "地球物理"],
        "心理": ["心理学", "应用心理学", "认知科学", "行为科学"],
        
        # 商科与经济类
        "金融": ["金融", "经济学", "投资学", "保险学", "金融工程"],
        "经济": ["经济学", "金融", "国际经济", "计量经济学", "发展经济学"],
        "管理": ["管理学", "工商管理", "企业管理", "公共管理", "人力资源"],
        "会计": ["会计学", "财务管理", "审计学", "税务学"],
        "市场": ["市场营销", "国际商务", "品牌管理", "数字营销"],
        "物流": ["物流管理", "供应链管理", "运输工程", "国际贸易"],
        "商业": ["商业分析", "商业智能", "电子商务", "创业管理"],
        
        # 医学与健康类
        "医学": ["临床医学", "基础医学", "预防医学", "医学影像", "口腔医学"],
        "护理": ["护理学", "助产学", "康复治疗", "健康管理"],
        "药学": ["药学", "中药学", "临床药学", "药物化学", "制药工程"],
        "公共卫生": ["公共卫生", "流行病学", "卫生统计", "环境卫生", "营养学"],
        "康复": ["康复治疗", "物理治疗", "作业治疗", "运动康复"],
        
        # 人文社科类
        "教育": ["教育学", "教育技术", "学前教育", "特殊教育", "体育教育"],
        "法学": ["法学", "知识产权", "国际法", "经济法", "刑法"],
        "社会": ["社会学", "社会工作", "人类学", "政治学", "国际关系"],
        "新闻": ["新闻学", "传播学", "广告学", "编辑出版", "数字媒体"],
        "语言": ["英语", "汉语言文学", "翻译", "对外汉语", "语言学"],
        "历史": ["历史学", "考古学", "文物保护", "博物馆学"],
        "哲学": ["哲学", "逻辑学", "宗教学", "伦理学"],
        
        # 艺术设计类
        "设计": ["艺术设计", "工业设计", "平面设计", "环境设计", "服装设计"],
        "建筑": ["建筑学", "城市规划", "景观建筑", "室内设计"],
        "艺术": ["美术学", "音乐学", "舞蹈学", "戏剧学", "电影学"],
        "传媒": ["广播电视", "动画", "数字媒体艺术", "影视制作"],
        
        # 农业与食品类
        "农业": ["农学", "园艺", "植物保护", "农业资源", "动物科学"],
        "食品": ["食品科学", "食品工程", "营养学", "食品安全", "酿酒工程"],
        "林业": ["林学", "园林", "森林保护", "木材科学"],
        "水产": ["水产养殖", "海洋科学", "渔业科学", "水族科学"],
        
        # 其他交叉学科
        "体育": ["体育教育", "运动训练", "社会体育", "运动人体科学"],
        "旅游": ["旅游管理", "酒店管理", "会展经济", "休闲体育"],
        "能源": ["新能源科学", "能源工程", "核工程", "可再生能源"]
    }
    
    keywords = []
    for key, values in discipline_mapping.items():
        if key in target_major_direction:
            keywords.extend(values)
    
    return list(set(keywords)) if keywords else []

def _get_related_disciplines(undergraduate_major: str, inferred_interest_domain: str = None) -> List[str]:
    """
    基于本科专业和推断兴趣领域获取相关学科
    """
    # 定义专业间的相关性映射
    related_mapping = {
        # 计算机与信息技术类专业的相关领域
        "计算机": ["计算机科学", "软件工程", "人工智能", "数据科学", "信息技术", "网络工程", "信息安全"],
        "软件": ["计算机科学", "软件工程", "信息技术", "系统工程", "数据科学"],
        "信息": ["计算机科学", "信息技术", "数据科学", "信息系统", "信息管理"],
        "人工智能": ["计算机科学", "数据科学", "机器学习", "认知科学", "统计学", "数学"],
        "数据科学": ["计算机科学", "统计学", "数学", "商业分析", "人工智能", "经济学"],
        "网络": ["网络工程", "信息安全", "计算机科学", "通信工程", "电子工程"],
        
        # 工程技术类专业的相关领域
        "电子": ["电子工程", "电气工程", "通信工程", "微电子", "计算机科学", "物理学"],
        "电气": ["电气工程", "电子工程", "自动化", "控制工程", "机械工程"],
        "通信": ["通信工程", "电子工程", "信息工程", "网络工程", "计算机科学"],
        "机械": ["机械工程", "自动化", "制造工程", "工业工程", "材料科学", "物理学"],
        "土木": ["土木工程", "建筑工程", "环境工程", "交通工程", "水利工程", "材料科学"],
        "材料": ["材料科学", "化学工程", "物理学", "化学", "机械工程", "电子工程"],
        "化工": ["化学工程", "化学", "材料科学", "环境工程", "生物工程", "石油工程"],
        "航空": ["航空工程", "航天工程", "机械工程", "材料科学", "物理学", "控制工程"],
        "生物工程": ["生物医学工程", "生物技术", "医学工程", "化学工程", "生物科学"],
        "环境": ["环境工程", "化学工程", "生态学", "地理科学", "公共卫生", "土木工程"],
        
        # 理学类专业的相关领域
        "数学": ["数学", "统计学", "数据科学", "金融数学", "计算机科学", "物理学"],
        "统计": ["统计学", "数学", "数据科学", "经济学", "心理学", "公共卫生"],
        "物理": ["物理学", "材料科学", "工程物理", "数学", "天文学", "地球科学"],
        "化学": ["化学", "材料科学", "化学工程", "生物化学", "药学", "环境科学"],
        "生物": ["生物科学", "生物技术", "生物医学", "医学", "环境科学", "农学"],
        "心理": ["心理学", "认知科学", "教育学", "社会学", "医学", "统计学"],
        "地理": ["地理科学", "环境科学", "测绘工程", "城市规划", "地质学", "生态学"],
        "地质": ["地质学", "地球科学", "环境科学", "地理科学", "材料科学", "物理学"],
        
        # 商科与经济类专业的相关领域
        "金融": ["金融", "经济学", "投资学", "风险管理", "数学", "统计学", "会计学"],
        "经济": ["经济学", "金融", "国际贸易", "统计学", "数学", "政治学", "社会学"],
        "管理": ["管理学", "工商管理", "人力资源", "心理学", "经济学", "社会学"],
        "会计": ["会计学", "金融", "经济学", "管理学", "数学", "统计学"],
        "市场": ["市场营销", "心理学", "统计学", "管理学", "传播学", "设计学"],
        "物流": ["物流管理", "供应链管理", "运筹学", "管理学", "工业工程", "经济学"],
        "商业": ["商业分析", "数据科学", "统计学", "管理学", "经济学", "计算机科学"],
        
        # 医学与健康类专业的相关领域
        "医学": ["临床医学", "基础医学", "生物医学", "生物科学", "化学", "心理学"],
        "护理": ["护理学", "医学", "心理学", "社会学", "管理学", "公共卫生"],
        "药学": ["药学", "化学", "生物科学", "医学", "化学工程", "生物工程"],
        "公共卫生": ["公共卫生", "医学", "统计学", "社会学", "环境科学", "管理学"],
        "康复": ["康复治疗", "医学", "心理学", "运动科学", "生物医学工程"],
        
        # 人文社科类专业的相关领域
        "教育": ["教育学", "心理学", "管理学", "社会学", "哲学", "数字媒体"],
        "法学": ["法学", "政治学", "社会学", "经济学", "哲学", "管理学"],
        "社会": ["社会学", "心理学", "政治学", "人类学", "教育学", "管理学"],
        "新闻": ["新闻学", "传播学", "社会学", "政治学", "文学", "数字媒体"],
        "语言": ["语言学", "文学", "教育学", "心理学", "传播学", "计算机科学"],
        "历史": ["历史学", "文学", "哲学", "考古学", "社会学", "政治学"],
        "哲学": ["哲学", "逻辑学", "心理学", "社会学", "政治学", "宗教学"],
        "政治": ["政治学", "国际关系", "法学", "经济学", "社会学", "历史学"],
        
        # 艺术设计类专业的相关领域
        "设计": ["设计学", "艺术学", "计算机科学", "心理学", "工程学", "市场营销"],
        "建筑": ["建筑学", "城市规划", "土木工程", "艺术学", "环境科学", "历史学"],
        "艺术": ["艺术学", "设计学", "文学", "历史学", "心理学", "教育学"],
        "传媒": ["传播学", "艺术学", "计算机科学", "心理学", "社会学", "市场营销"],
        
        # 农业与食品类专业的相关领域
        "农业": ["农学", "生物科学", "环境科学", "化学", "经济学", "管理学"],
        "食品": ["食品科学", "化学", "生物科学", "营养学", "化学工程", "管理学"],
        "林业": ["林学", "生态学", "环境科学", "生物科学", "地理科学", "管理学"],
        "水产": ["水产科学", "生物科学", "环境科学", "海洋科学", "食品科学"],
        
        # 其他交叉学科专业的相关领域
        "体育": ["体育学", "医学", "心理学", "教育学", "管理学", "康复治疗"],
        "旅游": ["旅游管理", "管理学", "经济学", "地理科学", "心理学", "市场营销"],
        "能源": ["能源工程", "物理学", "化学工程", "环境工程", "机械工程", "电气工程"],
        
        # 新兴交叉领域
        "金融科技": ["金融", "计算机科学", "数据科学", "数学", "统计学", "经济学"],
        "生物信息": ["生物科学", "计算机科学", "数学", "统计学", "医学", "化学"],
        "智能制造": ["机械工程", "计算机科学", "自动化", "工业工程", "材料科学"],
        "数字媒体": ["计算机科学", "艺术学", "传播学", "心理学", "市场营销", "设计学"],
        "可持续发展": ["环境科学", "经济学", "管理学", "工程学", "政治学", "社会学"]
    }
    
    related_disciplines = []
    
    # 基于本科专业匹配
    for key, values in related_mapping.items():
        if key in undergraduate_major:
            related_disciplines.extend(values)
    
    # 基于推断兴趣领域补充
    if inferred_interest_domain:
        for key, values in related_mapping.items():
            if key in inferred_interest_domain.lower():
                related_disciplines.extend(values)
    
    return list(set(related_disciplines)) if related_disciplines else []

def _create_candidate_dict(row, match_source: str) -> Dict[str, Any]:
    """
    创建候选项目字典
    """
    return {
                "id": row.id,
                "program_name_cn": row.program_name_cn,
                "program_name_en": row.program_name_en,
                "program_direction": row.program_direction,
                "faculty": row.faculty,
                "program_objectives": row.program_objectives,
                "degree": row.degree,
                "program_duration": row.program_duration,
                "program_tuition": row.program_tuition,
                "gpa_requirements": row.gpa_requirements,
                "embedding": row.embedding,
                # 学校信息
                "school_name_cn": row.school_name_cn,
                "school_name_en": row.school_name_en,
                "school_qs_rank": row.school_qs_rank,
                "school_region": row.school_region,
                "region_name": row.school_region,
                # 使用新的排名解析工具推断学校层级
                "school_tier": infer_school_tier_from_qs_rank(row.school_qs_rank),
                "match_source": match_source  # 标记匹配来源
            }

async def _vector_based_filtering(
    user_profile: EnhancedUserProfile,
    exclude_program_ids: Set[int],
    limit: int = 10,
    similarity_threshold: float = 0.5
) -> List[Dict[str, Any]]:
    """
    使用向量检索方法补充候选池
    
    Args:
        user_profile: 增强用户画像
        exclude_program_ids: 要排除的专业ID集合
        limit: 需要返回的候选数量 
        similarity_threshold: 相似度阈值，低于此值的专业将被过滤
        
    Returns:
        向量检索的候选池
    """
    async for session in get_db():
        target_regions = user_profile.target_regions
        target_degree = user_profile.target_degree
        target_major_direction = user_profile.target_major_direction
        
        # 构建ORM查询 - 直接查询programs表
        query = (
            select(
                Program.id,
                Program.program_name_cn,
                Program.program_name_en,
                Program.program_direction,
                Program.faculty,
                Program.program_objectives,
                Program.degree,
                Program.program_duration,
                Program.program_tuition,
                Program.gpa_requirements,
                Program.embedding,
                # 学校信息
                Program.school_name_cn,
                Program.school_name_en,
                Program.school_qs_rank,
                Program.school_region
            )
            .where(
                and_(
                    Program.degree == target_degree,
                    Program.school_region.in_(target_regions),
                    Program.embedding.isnot(None),  # 只选择有嵌入向量的专业
                    ~Program.id.in_(exclude_program_ids) if exclude_program_ids else True
                )
            )
        )
        
        result = await session.execute(query)
        rows = result.fetchall()
        
        # 如果没有结果，直接返回空列表
        if not rows:
            print("没有找到带有嵌入向量的专业项目")
            return []
        
        # 将结果转换为字典列表，并提取嵌入向量
        programs = []
        program_vectors = []
        
        for row in rows:
            program_dict = {
                "id": row.id,
                "program_name_cn": row.program_name_cn,
                "program_name_en": row.program_name_en,
                "program_direction": row.program_direction,
                "faculty": row.faculty,
                "program_objectives": row.program_objectives,
                "degree": row.degree,
                "program_duration": row.program_duration,
                "program_tuition": row.program_tuition,
                "gpa_requirements": row.gpa_requirements,
                "embedding": row.embedding,
                # 学校信息
                "school_name": row.school_name_cn,
                "school_name_cn": row.school_name_cn,
                "school_name_en": row.school_name_en,
                "school_qs_rank": row.school_qs_rank,
                "school_region": row.school_region,
                "region_name": row.school_region,
                # 使用新的排名解析工具推断学校层级
                "school_tier": infer_school_tier_from_qs_rank(row.school_qs_rank)
            }
            
            # 解析嵌入向量
            try:
                # 检查嵌入向量的类型，处理不同的存储格式
                if isinstance(row.embedding, str):
                    # 如果是字符串，检查是否为空或无效
                    if not row.embedding.strip():
                        print(f"专业 ID {row.id} 的嵌入向量为空字符串")
                        continue
                    # 使用json.loads解析
                    try:
                        embedding = json.loads(row.embedding)
                    except (json.JSONDecodeError, ValueError) as json_error:
                        print(f"专业 ID {row.id} 的嵌入向量JSON解析失败: {json_error}, 内容: {row.embedding[:100]}...")
                        continue
                elif isinstance(row.embedding, (list, tuple)):
                    # 如果已经是列表或元组，直接使用
                    embedding = row.embedding
                elif row.embedding is None:
                    # 如果嵌入向量为空，跳过此专业
                    print(f"专业 ID {row.id} 的嵌入向量为空")
                    continue
                else:
                    # 其他类型，尝试转换为列表
                    try:
                        embedding = list(row.embedding)
                    except (TypeError, ValueError):
                        print(f"专业 ID {row.id} 的嵌入向量格式不支持: {type(row.embedding)}")
                        continue
                
                # 验证embedding是否为有效的数值列表
                if not embedding or not isinstance(embedding, (list, tuple)):
                    print(f"专业 ID {row.id} 的嵌入向量不是有效的列表格式")
                    continue
                
                # 检查是否包含有效的数值
                try:
                    embedding_vector = np.array(embedding, dtype=float)
                    if embedding_vector.size == 0:
                        print(f"专业 ID {row.id} 的嵌入向量为空数组")
                        continue
                except (ValueError, TypeError) as e:
                    print(f"专业 ID {row.id} 的嵌入向量无法转换为数值数组: {e}")
                    continue
                
                # 添加到列表
                programs.append(program_dict)
                program_vectors.append(embedding_vector)
            except Exception as e:
                print(f"处理专业 ID {row.id} 的嵌入向量时出错: {e}")
                continue
        
        # 如果没有有效的程序向量，直接返回空列表
        if not program_vectors:
            print("没有找到有效的程序向量")
            return []
        
        # 为目标专业方向生成嵌入向量
        try:
            target_embedding = embed_text(target_major_direction)
            
            # 计算相似度
            similarities = cosine_similarity([target_embedding], program_vectors)[0]
            
            # 创建(索引,相似度)元组列表并排序
            indexed_sims = [(i, sim) for i, sim in enumerate(similarities)]
            indexed_sims.sort(key=lambda x: x[1], reverse=True)
            
            # 过滤低于阈值的结果
            filtered_results = []
            for idx, sim in indexed_sims:
                if sim >= similarity_threshold:
                    program = programs[idx].copy()
                    program["vector_similarity"] = float(sim)
                    program["match_source"] = "vector_filtering"  # 标记来源
                    filtered_results.append(program)
            
            # 在相似度筛选的基础上，按QS排名进行二次排序
            # 首先按相似度排序（保持语义相关性），然后在相同相似度下按排名排序
            filtered_results.sort(key=lambda x: (
                -x["vector_similarity"],  # 相似度降序（越高越好）
                get_ranking_sort_key(x.get("school_qs_rank", ""))[1]  # 排名升序（越小越好）
            ))
            
            # 应用学校多样性限制
            diverse_results = await _apply_school_diversity_limit(filtered_results, user_profile)
            
            # 限制返回数量
            return diverse_results[:limit]
        except Exception as e:
            print(f"向量检索过程出错: {e}")
            return []

async def _apply_school_diversity_limit(
    candidates: List[Dict[str, Any]], 
    user_profile: EnhancedUserProfile, 
    max_per_school: int = MAX_PROGRAMS_PER_SCHOOL
) -> List[Dict[str, Any]]:
    """
    应用学校多样性限制，确保每所学校最多出现指定数量的专业
    通过计算用户画像与专业embedding的相似度，保留最相关的专业
    
    Args:
        candidates: 候选专业列表
        user_profile: 增强用户画像
        max_per_school: 每所学校最大专业数量
        
    Returns:
        经过多样性限制的候选列表
    """
    if not candidates:
        return candidates
    
    # 1. 生成用户画像查询向量
    # 结合本科专业和目标专业，全面反映用户背景和意向
    try:
        query_text = f"{user_profile.undergraduate_major} {user_profile.target_major_direction}"
        query_embedding = embed_text(query_text)
    except Exception as e:
        print(f"生成用户画像查询向量时出错: {e}, 将退回至默认排序")
        # 如果生成用户向量失败，则退回至旧的排序逻辑
        return _fallback_diversity_limit(candidates, max_per_school)

    # 2. 按学校分组
    school_groups = defaultdict(list)
    for candidate in candidates:
        school_name = candidate.get("school_name_cn")
        school_groups[school_name].append(candidate)
    
    diverse_candidates = []
    
    # 3. 对每个学校的专业进行个性化排序和筛选
    for school_name, school_candidates in school_groups.items():
        # 如果该学校的专业数量无需筛选，直接添加
        if len(school_candidates) <= max_per_school:
            diverse_candidates.extend(school_candidates)
            continue

        # 准备有效的专业向量
        candidate_vectors = []
        valid_candidates = []
        candidates_with_no_vector = []

        for cand in school_candidates:
            if cand.get('embedding'):
                try:
                    vec = np.array(json.loads(cand['embedding']), dtype=float)
                    if vec.size > 0:
                        candidate_vectors.append(vec)
                        valid_candidates.append(cand)
                    else:
                        candidates_with_no_vector.append(cand)
                except (json.JSONDecodeError, TypeError, ValueError):
                    candidates_with_no_vector.append(cand)
            else:
                candidates_with_no_vector.append(cand)
        
        # 如果没有有效的专业向量，则使用旧逻辑
        if not valid_candidates:
            diverse_candidates.extend(_fallback_diversity_limit(school_candidates, max_per_school))
            continue
            
        # 计算相似度
        similarities = cosine_similarity([query_embedding], candidate_vectors)[0]
        
        # 将相似度分数添加到专业信息中
        for i, cand in enumerate(valid_candidates):
            cand['similarity_score'] = similarities[i]
        
        # 给没有向量的专业一个最低分
        for cand in candidates_with_no_vector:
            cand['similarity_score'] = -1.0
            
        # 合并并按相似度降序排序
        all_school_candidates = valid_candidates + candidates_with_no_vector
        all_school_candidates.sort(key=lambda x: x.get('similarity_score', -1.0), reverse=True)
        
        # 选取前N个专业
        selected_candidates = all_school_candidates[:max_per_school]
        diverse_candidates.extend(selected_candidates)
        
        # 记录多样性控制的效果
        if len(school_candidates) > max_per_school:
            print(f"  学校 {school_name}: 从 {len(school_candidates)} 个专业中基于个性化相似度选择了 {len(selected_candidates)} 个")

    # 最后按原始候选池的顺序（通常是QS排名）对整体结果进行一次稳定排序
    # 这有助于保持推荐的整体排名感
    final_sorted_list = sorted(diverse_candidates, key=lambda x: get_ranking_sort_key(x.get("school_qs_rank", "")))
    
    return final_sorted_list

def _fallback_diversity_limit(
    candidates: List[Dict[str, Any]], 
    max_per_school: int
) -> List[Dict[str, Any]]:
    """
    当无法使用向量相似度时，提供一个备选的多样性限制策略。
    简单地截断。
    """
    school_groups = defaultdict(list)
    for candidate in candidates:
        school_name = candidate.get("school_name_cn")
        school_groups[school_name].append(candidate)
        
    diverse_candidates = []
    for school_name, school_candidates in school_groups.items():
        # 按QS排名排序（假定外部已排序，这里是保险措施）
        school_candidates.sort(key=lambda x: get_ranking_sort_key(x.get("school_qs_rank", "")))
        diverse_candidates.extend(school_candidates[:max_per_school])
        
    return diverse_candidates

async def generate_hard_filtered_results(
    user_profile: EnhancedUserProfile,
    dynamic_qs_range: Optional[Tuple[Optional[int], Optional[int]]] = None
) -> List[Dict[str, Any]]:
    """
    硬筛选模式：只进行数据库筛选，返回所有符合条件的专业，不限数量
    
    Args:
        user_profile: 增强用户画像
        dynamic_qs_range: 动态计算的QS排名筛选范围
        
    Returns:
        硬筛选结果列表，按QS排名排序
    """
    print(f"\n=== 硬筛选模式：只进行数据库筛选 ===")
    print(f"目标专业方向: {user_profile.target_major_direction}")
    print(f"目标地区: {user_profile.target_regions}")
    print(f"目标学位: {user_profile.target_degree}")
    
    # 使用现有的ORM筛选逻辑，但不限制数量
    async for session in get_db():
        # 精确匹配
        exact_matches = await _get_exact_matches(
            session, user_profile, dynamic_qs_range
        )
        
        # 模糊匹配 - 排除已有的精确匹配
        fuzzy_matches = await _get_fuzzy_matches(
            session, user_profile, 
            {p["id"] for p in exact_matches},
            dynamic_qs_range
        )
        
        # 相关匹配 - 排除已有的精确和模糊匹配
        related_matches = await _get_related_matches(
            session, user_profile,
            {p["id"] for p in exact_matches + fuzzy_matches},
            dynamic_qs_range
        )
        
        # 合并所有结果
        all_matches = exact_matches + fuzzy_matches + related_matches
        
        # 按QS排名排序
        all_matches.sort(key=lambda x: get_ranking_sort_key(x.get("school_qs_rank", "")))
        
        # 统计结果
        exact_count = len(exact_matches)
        fuzzy_count = len(fuzzy_matches) 
        related_count = len(related_matches)
        total_count = len(all_matches)
        
        print(f"\n=== 硬筛选结果统计 ===")
        print(f"精确匹配: {exact_count} 个")
        print(f"模糊匹配: {fuzzy_count} 个")
        print(f"相关匹配: {related_count} 个")
        print(f"总计: {total_count} 个专业")
        
        return all_matches

        