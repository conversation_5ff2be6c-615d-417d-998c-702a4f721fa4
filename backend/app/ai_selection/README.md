# AI智能选校系统集成文档

## 系统概述

AI智能选校系统是一个基于大语言模型(LLM)和检索增强生成(RAG)的智能留学推荐系统，现已完全集成到TunshuEdu平台中。系统采用全异步架构，支持流式推荐，为用户提供个性化的院校和专业推荐服务。

## 🚀 核心功能

### ✨ 主要特性

- **智能画像构建**: 基于LLM增强用户输入，自动提取技能标签和兴趣领域
- **多维度匹配**: 院校层级、专业方向、经历背景、学术表现四维评估
- **流式推荐**: 实时推送推荐进度，优化用户体验
- **案例驱动**: 基于真实申请案例进行院校可达性分析
- **向量检索**: 当数据库筛选结果不足时，自动启用向量相似度补充
- **异步并发**: 全面优化的异步处理，支持高并发推荐生成

### 🎯 推荐流程

1. **用户画像增强** - LLM分析用户背景，提取关键信息
2. **候选池生成** - 多层次筛选策略，确保候选项目质量
3. **院校可达性分析** - 基于同校和同层级案例评估申请难度
4. **多维度评分** - 综合评估专业匹配度和申请成功率
5. **智能排序推荐** - 按QS排名优化的个性化推荐

## 📁 目录结构

```
app/ai_selection/
├── __init__.py
├── README.md                    # 本文档
├── config.py                    # 系统配置
│
├── api/                         # API接口层
│   ├── __init__.py
│   ├── router.py               # 路由注册
│   └── endpoints/
│       ├── __init__.py
│       ├── recommendation.py   # 推荐接口 (流式+标准)
│       └── data.py            # 数据查询接口
│
├── core/                       # 核心业务逻辑
│   ├── __init__.py
│   ├── user_profile.py         # 用户画像构建
│   ├── candidate_pool.py       # 候选池生成 (多层筛选)
│   ├── school_matching.py      # 院校匹配 (案例驱动)
│   ├── program_matching.py     # 专业匹配 (异步并发)
│   ├── ranking.py              # 排序推荐生成
│   └── streaming_ranking.py    # 流式推荐 (实时推送)
│
├── db/                         # 数据库层
│   ├── __init__.py
│   ├── models.py               # 数据模型 (Programs + Cases)
│   ├── seed.py                 # 向量嵌入生成
│   └── init_db.py             # 数据库初始化
│
├── schemas/                    # 数据模型定义
│   ├── __init__.py
│   ├── student_profile.py      # 新版学生画像 (前端输入)
│   ├── user.py                 # 增强用户画像 (内部使用)
│   ├── recommendation.py       # 推荐响应模型
│   ├── program.py              # 专业项目模型
│   └── case.py                 # 申请案例模型
│
└── utils/                      # 工具模块
    ├── __init__.py
    ├── adapter.py              # 数据适配器 (新增)
    ├── llm.py                  # LLM服务 (异步优化)
    ├── rag.py                  # RAG检索 (向量搜索)
    ├── language_parser.py      # 语言成绩解析
    └── ranking_parser.py       # 排名解析工具
```

## 🔄 数据流架构

### 输入数据结构 (StudentProfile)

系统采用全新的结构化输入格式，支持前端表单的精确映射：

```json
{
  "academic": {
    "education": "master",           // 目标申请学历
    "school": "华南理工大学",        // 就读院校
    "major": "微电子科学与工程",     // 专业
    "gpa": "85.5",                  // 成绩
    "gpaScale": "100",              // 成绩制式
    "ranking": "10%"                // 班级排名(可选)
  },
  "intention": {
    "countries": ["Hong Kong", "Singapore"],           // 意向地区
    "majors": ["computer_science", "electrical_engineering"], // 意向专业
    "duration": "1"                 // 期望项目时长
  },
  "strength": {
    "competition": ["national"],     // 竞赛经历
    "internship": ["internet"],      // 实习经历  
    "research": ["core-paper"],      // 科研经历
    "rankingPreference": ["QS-50"]   // 排名偏好
  }
}
```

### 数据适配转换

新增的 `adapter.py` 模块提供智能数据转换：

- **GPA标准化**: 自动转换不同GPA制式到100分制
- **地区名称映射**: 英文地区名转换为中文数据库标准
- **专业领域扩展**: 智能扩展专业代码到具体专业名称
- **软实力文本化**: 将结构化实力数据转换为可分析文本
- **院校层级推断**: 根据院校名称推断985/211层级

## 🗄️ 数据库设计

### 核心表结构

系统采用简化的两表设计，优化查询性能：

#### 1. `ai_selection_programs` - 专业项目表
包含完整的学校和专业信息，避免复杂关联查询：

**学校维度**:
- `school_name_cn/en` - 学校中英文名称
- `school_qs_rank` - QS世界排名  
- `school_region` - 所在地区

**专业维度**:
- `program_name_cn/en` - 专业中英文名称
- `program_direction` - 专业方向分类
- `degree` - 申请学位类型
- `program_duration` - 项目时长
- `faculty` - 所在学院

**申请维度**:
- `gpa_requirements` - GPA要求
- `language_requirements` - 语言要求
- `application_time` - 申请时间窗口
- `program_tuition` - 学费信息

**向量维度**:
- `embedding` - 专业描述的向量嵌入(JSONB)

#### 2. `ai_selection_cases` - 申请案例表
存储真实申请案例，支持可达性分析：

**学生背景**:
- `undergraduate_school/tier/major` - 本科背景
- `gpa` - 学术成绩
- `language_score` - 语言成绩(JSONB)
- `key_experiences` - 关键经历

**录取结果**:
- `offer_region/degree/major_direction` - 录取信息
- `offer_program_id` - 录取专业ID(外键)
- `embedding` - 案例背景的向量嵌入(JSONB)

### 数据库特性

- **无冗余关联**: 专业表包含完整学校信息，减少JOIN操作
- **向量检索就绪**: 内置嵌入向量，支持语义相似度搜索
- **硬筛选优化**: 支持项目时长、排名偏好等硬性条件过滤
- **案例权重化**: 区分同校案例和同层级案例，提供精准可达性分析

## 🔌 API接口文档

### 1. 流式推荐接口 (推荐)

**端点**: `POST /api/ai-selection/recommendation/recommend/stream`

**特性**:
- Server-Sent Events (SSE) 协议
- 实时推送推荐进度和结果
- 并发处理优化，支持边计算边推送
- 异常处理和错误恢复

**请求头**:
```
Content-Type: application/json
Accept: text/event-stream
```

**响应流事件类型**:
```typescript
// 开始事件
{type: 'start', message: '开始分析您的背景...'}

// 进度事件  
{type: 'progress', stage: 'profile', message: '用户画像构建完成', elapsed: 1.23}
{type: 'progress', stage: 'candidates', message: '找到 15 个候选专业', elapsed: 2.45}
{type: 'progress', stage: 'school_matching', message: '院校匹配分析完成，发现 8 所可达院校', elapsed: 3.67}

// 预览事件(TOP5)
{type: 'preview', stage: 'top_preview', preview_schools: [...]}

// 推荐完成事件
{type: 'recommendation_complete', rank: 1, recommendation: {...}, generation_time: 2.1}

// 完成事件
{type: 'complete', message: '推荐生成完成', total_elapsed: 15.2}

// 错误事件
{type: 'error', message: '错误描述'}
```

### 2. 标准推荐接口

**端点**: `POST /api/ai-selection/recommendation/recommend`

返回完整的推荐结果，适用于不需要实时反馈的场景。

### 3. 数据查询接口

- `GET /api/ai-selection/data/programs` - 专业项目列表 (支持多维筛选)
- `GET /api/ai-selection/data/cases` - 申请案例列表
- `GET /api/ai-selection/data/schools` - 学校列表 (从专业表提取)
- `GET /api/ai-selection/data/regions` - 地区列表
- `GET /api/ai-selection/data/statistics` - 系统统计信息

**查询支持**:
- 分页 (`limit`, `offset`)
- 多字段筛选 (学校、地区、专业、学位等)
- 智能排序 (QS排名优先)

## ⚙️ 系统配置

### 核心参数 (`config.py`)

```python
# 推荐策略
CANDIDATE_POOL_SIZE = 20         # 候选池大小
FINAL_RECOMMENDATIONS = 10       # 最终推荐数量
GPA_TOLERANCE = 0.05            # GPA匹配容差(±5%)
MIN_CASE_THRESHOLD = 3          # 最小案例阈值

# 向量检索
VECTOR_SIMILARITY_THRESHOLD = 0.8  # 相似度阈值

# 评分权重  
WEIGHTS = {
    "school_tier_match": 0.4,           # 院校层级匹配
    "program_direction_match": 0.3,     # 专业方向匹配  
    "experience_match": 0.2,            # 经历匹配
    "academic_performance_match": 0.1   # 学术表现匹配
}

# API密钥
SILICONE_FLOW_API_KEY = "..."    # LLM服务密钥
```

## 🤖 AI能力架构

### LLM增强功能

**用户画像增强** (`llm.py`):
- 自动提取关键技能标签
- 推断兴趣领域  
- 评估学术潜力
- 支持异步并发调用

**推荐理由生成**:
- 个性化推荐理由
- 结合案例数据分析
- 多维度匹配解释

### RAG检索能力

**向量嵌入** (`rag.py`):
- SiliconeFlow嵌入服务集成
- 支持中英文语义理解
- 自动重试和异常处理

**语义搜索**:
- 专业描述相似度匹配
- 案例背景相似度分析
- 混合检索(关键词+向量)

### 候选池生成策略

**三层筛选机制** (`candidate_pool.py`):

1. **精确匹配**: 专业名称关键词匹配
2. **模糊匹配**: 学科领域相关性匹配  
3. **关联匹配**: 本科专业相关领域扩展

**硬性筛选条件**:
- 地区、学位、专业方向匹配
- 项目时长精确筛选 (支持"3年及以上")
- QS排名上限筛选 (正则提取+数值比较)

**智能补充机制**:
- 当数据库筛选结果<10个时自动启用向量检索
- 相似度阈值过滤，确保语义相关性
- 按QS排名二次排序

## 📊 性能优化

### 异步并发架构

**并发处理策略**:
- 专业匹配评估: 5个一批并发处理
- 推荐理由生成: 全并发+as_completed流式输出
- LLM API调用: 异步请求池

**批处理优化**:
- 数据库查询批量化
- 向量嵌入批量生成
- 进度实时反馈

### 缓存和检索优化

**数据库优化**:
- 索引优化 (学校名、地区、专业方向)
- SQL查询优化 (减少JOIN)
- 连接池管理

**向量检索优化**:
- 相似度阈值过滤
- Top-K限制
- 内存向量缓存

## 🔧 部署和运维

### 初始化步骤

1. **安装依赖**
   ```bash
   cd backend
   pip install -r requirements.txt
   ```

2. **数据库迁移**
   ```bash
   alembic upgrade head
   ```

3. **生成向量嵌入** (仅新数据需要)
   ```bash
   python -m app.ai_selection.db.init_db
   ```

4. **启动服务**
   ```bash
   python main.py  # 主服务自动包含AI选校路由
   ```

### 监控和维护

**系统监控**:
- API响应时间监控
- LLM调用成功率
- 向量检索性能
- 数据库查询优化

**数据维护**:
- 定期更新专业项目数据
- 新增申请案例录入
- 向量嵌入更新
- 配置参数调优

## 🚨 注意事项

### API限制
- LLM服务调用频率限制
- 向量嵌入服务配额
- 数据库连接池限制

### 数据质量
- 专业数据的及时性
- 案例数据的真实性  
- 向量嵌入的有效性

### 性能考量
- 大批量并发推荐的资源消耗
- 长时间流式连接的稳定性
- 向量检索的内存使用

## 🔮 未来发展

### 功能扩展
- [ ] 多语言支持 (英文界面)
- [ ] 更细粒度的专业标签体系
- [ ] 基于用户反馈的推荐优化
- [ ] 申请时间线和截止日期提醒
- [ ] 费用预算和奖学金信息集成

### 技术优化  
- [ ] 更先进的向量检索技术
- [ ] 图神经网络推荐算法
- [ ] 实时学习和模型更新
- [ ] 边缘计算和CDN优化

### 数据增强
- [ ] 更多地区和院校数据
- [ ] 就业前景和薪资数据
- [ ] 校友网络和行业分布
- [ ] 实时排名和政策更新

---

## 📞 技术支持

如有技术问题或需要功能定制，请联系开发团队。

**文档版本**: v2.0.0  
**最后更新**: 2024年12月  
**兼容性**: Python 3.8+, PostgreSQL 12+, FastAPI 0.100+ 

# AI选校系统 - 前端对接文档 v3.0

## 📋 版本更新说明

### v3.0 新增功能：动态权重系统
- 用户可以主动选择申请偏好类型（排名导向/专业导向/平衡型）
- 系统根据偏好类型动态调整推荐权重
- 新增院校排名评分维度
- 优化候选池筛选和展示逻辑

---

## 🎯 核心功能概览

### 1. 申请偏好类型选择
用户可以选择以下三种申请偏好：

- **排名导向型** (`ranking_focused`)：更看重学校QS排名
- **专业导向型** (`major_focused`)：更看重专业匹配度
- **平衡型** (`balanced`)：综合考虑排名和专业

### 2. 动态权重调整
系统根据用户选择的偏好类型，自动调整以下五个维度的权重：

| 评分维度 | 排名导向型 | 专业导向型 | 平衡型 |
|---------|-----------|-----------|-------|
| 院校层级匹配 | 20% | 25% | 30% |
| **院校排名评分** | **40%** | **10%** | **20%** |
| **专业方向匹配** | **20%** | **45%** | **30%** |
| 经历匹配 | 15% | 15% | 15% |
| 学术表现匹配 | 5% | 5% | 5% |

---

## 🔌 前端集成指南

### 1. 表单字段更新

#### 新增字段：申请偏好类型
```html
<div class="form-group">
    <label for="preferenceType">申请偏好类型</label>
    <select id="preferenceType" required>
        <option value="balanced" selected>平衡型（综合考虑排名和专业）</option>
        <option value="ranking_focused">排名导向型（更看重学校排名）</option>
        <option value="major_focused">专业导向型（更看重专业匹配）</option>
    </select>
</div>
```

#### 字段说明
- **字段名称**: `preference_type`
- **类型**: `string`
- **必填**: 是
- **默认值**: `"balanced"`
- **可选值**: 
  - `"ranking_focused"` - 排名导向型
  - `"major_focused"` - 专业导向型  
  - `"balanced"` - 平衡型

### 2. 数据提交格式

#### 更新后的请求体结构
```javascript
const requestData = {
    academic: {
        education: "master",
        school: "华南理工大学", 
        major: "微电子科学与工程",
        gpa: "85.5",
        gpaScale: "100",
        ranking: "10%"  // 可选
    },
    intention: {
        countries: ["Hong Kong", "Singapore"],
        majors: ["computer_science", "electrical_engineering"],
        duration: "1"
    },
    strength: {
        competition: ["national"],
        internship: ["internet"],
        research: [],
        rankingPreference: ["QS-50", "QS-100"],
        preference_type: "ranking_focused"  // 🆕 新增字段
    }
};
```

### 3. API响应变更

#### 流式推荐新增事件

**用户偏好确认事件**
```javascript
{
    "type": "user_preference_detected",
    "preference_type": "ranking_focused",
    "reason": "您选择了排名导向型，系统将优先推荐排名较高的院校"
}
```

**权重配置事件**
```javascript
{
    "type": "weights_applied", 
    "weights": {
        "school_tier_match": 0.2,
        "school_ranking_score": 0.4,  // 🆕 新增维度
        "program_direction_match": 0.2,
        "experience_match": 0.15,
        "academic_performance_match": 0.05
    }
}
```

**候选池最终评分事件（增强）**
```javascript
{
    "type": "candidate_pool_finalized",
    "candidates": [
        {
            "rank": 1,
            "school_name_cn": "香港大学",
            "program_name_cn": "计算机科学",
            "total_match": 0.856,
            "school_tier_match": 0.9,
            "school_ranking_score": 0.95,  // 🆕 新增字段
            "program_direction_match": 0.8,
            "experience_match": 0.7,
            "academic_performance_match": 0.85
        }
        // ... 更多候选项目
    ]
}
```

### 4. 推荐结果变更

#### 详细评分包含新维度
```javascript
{
    "type": "recommendation_complete",
    "recommendation": {
        "rank": 1,
        "school_name": "香港大学",
        "program_name_cn": "计算机科学硕士",
        "scores": {
            "school_tier_match": 0.9,
            "school_ranking_score": 0.95,     // 🆕 新增
            "program_direction_match": 0.8,
            "experience_match": 0.7,
            "academic_performance_match": 0.85,
            "total_match": 0.856
        },
        "recommendation_reason": "基于您的排名导向偏好...",
        "matching_cases_count": 15
    }
}
```

---

## 🎨 UI设计建议

### 1. 偏好选择组件

#### 推荐设计：卡片式选择器
```html
<div class="preference-selector">
    <h3>🎯 选择您的申请偏好</h3>
    <div class="preference-cards">
        <div class="preference-card" data-value="ranking_focused">
            <div class="card-icon">🏆</div>
            <div class="card-title">排名导向型</div>
            <div class="card-description">
                更看重学校QS排名<br>
                适合注重学历含金量的申请者
            </div>
        </div>
        
        <div class="preference-card" data-value="major_focused">
            <div class="card-icon">🎓</div>
            <div class="card-title">专业导向型</div>
            <div class="card-description">
                更看重专业匹配度<br>
                适合有明确专业目标的申请者
            </div>
        </div>
        
        <div class="preference-card active" data-value="balanced">
            <div class="card-icon">⚖️</div>
            <div class="card-title">平衡型</div>
            <div class="card-description">
                综合考虑排名和专业<br>
                推荐首次使用的申请者
            </div>
        </div>
    </div>
</div>
```

#### 对应CSS样式参考
```css
.preference-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}

.preference-card {
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.preference-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 8px rgba(0,123,255,0.2);
}

.preference-card.active {
    border-color: #007bff;
    background-color: #f0f8ff;
}

.card-icon {
    font-size: 2em;
    margin-bottom: 10px;
}

.card-title {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
}

.card-description {
    font-size: 0.9em;
    color: #666;
    line-height: 1.4;
}
```

### 2. 偏好信息展示区域

```html
<div id="preferenceInfo" class="preference-info" style="display: none;">
    <h3>🎯 您的申请偏好</h3>
    <div id="preferenceContent">
        <!-- 动态内容 -->
    </div>
</div>
```

### 3. 权重配置可视化

```html
<div id="weightInfo" class="weight-info" style="display: none;">
    <h3>⚖️ 推荐权重配置</h3>
    <div id="weightContent">
        <!-- 动态权重柱状图或饼图 -->
    </div>
</div>
```

---

## 📊 事件处理示例

### JavaScript事件处理代码
```javascript
// 1. 处理偏好选择
function handlePreferenceSelection() {
    const cards = document.querySelectorAll('.preference-card');
    cards.forEach(card => {
        card.addEventListener('click', function() {
            // 移除其他卡片的active状态
            cards.forEach(c => c.classList.remove('active'));
            // 添加当前卡片的active状态
            this.classList.add('active');
            // 更新隐藏的select值
            document.getElementById('preferenceType').value = this.dataset.value;
        });
    });
}

// 2. 处理流式事件
function handleStreamEvent(event) {
    switch (event.type) {
        case 'user_preference_detected':
            showPreferenceInfo(event);
            break;
        case 'weights_applied':
            showWeightConfig(event);
            break;
        case 'candidate_pool_finalized':
            updateCandidatePoolTable(event);
            break;
        case 'recommendation_complete':
            updateRecommendationCard(event);
            break;
    }
}

// 3. 显示偏好信息
function showPreferenceInfo(event) {
    const preferenceInfo = document.getElementById('preferenceInfo');
    const preferenceContent = document.getElementById('preferenceContent');
    
    const typeNames = {
        'ranking_focused': '排名导向型',
        'major_focused': '专业导向型', 
        'balanced': '平衡型'
    };
    
    preferenceContent.innerHTML = `
        <div class="preference-display">
            <span class="preference-badge">${typeNames[event.preference_type]}</span>
            <p>${event.reason}</p>
        </div>
    `;
    
    preferenceInfo.style.display = 'block';
}

// 4. 显示权重配置
function showWeightConfig(event) {
    const weightInfo = document.getElementById('weightInfo');
    const weightContent = document.getElementById('weightContent');
    
    const weights = event.weights;
    weightContent.innerHTML = `
        <div class="weight-bars">
            <div class="weight-item">
                <span>院校层级匹配</span>
                <div class="weight-bar">
                    <div class="weight-fill" style="width: ${weights.school_tier_match * 100}%"></div>
                </div>
                <span>${(weights.school_tier_match * 100).toFixed(1)}%</span>
            </div>
            <div class="weight-item">
                <span>院校排名评分</span>
                <div class="weight-bar">
                    <div class="weight-fill" style="width: ${weights.school_ranking_score * 100}%"></div>
                </div>
                <span>${(weights.school_ranking_score * 100).toFixed(1)}%</span>
            </div>
            <div class="weight-item">
                <span>专业方向匹配</span>
                <div class="weight-bar">
                    <div class="weight-fill" style="width: ${weights.program_direction_match * 100}%"></div>
                </div>
                <span>${(weights.program_direction_match * 100).toFixed(1)}%</span>
            </div>
            <div class="weight-item">
                <span>经历匹配</span>
                <div class="weight-bar">
                    <div class="weight-fill" style="width: ${weights.experience_match * 100}%"></div>
                </div>
                <span>${(weights.experience_match * 100).toFixed(1)}%</span>
            </div>
            <div class="weight-item">
                <span>学术表现匹配</span>
                <div class="weight-bar">
                    <div class="weight-fill" style="width: ${weights.academic_performance_match * 100}%"></div>
                </div>
                <span>${(weights.academic_performance_match * 100).toFixed(1)}%</span>
            </div>
        </div>
    `;
    
    weightInfo.style.display = 'block';
}
```

---

## 🧪 测试用例

### 快速测试数据
前端可以实现快速填充功能，帮助测试不同偏好类型：

```javascript
const testCases = {
    ranking_focused: {
        academic: {
            education: "master",
            school: "北京科技大学",
            major: "计算机科学与技术",
            gpa: "88.5",
            gpaScale: "100",
            ranking: "10%"
        },
        intention: {
            countries: ["Hong Kong", "Singapore", "UK"],
            majors: ["computer_science"],
            duration: "1"
        },
        strength: {
            competition: ["national"],
            internship: ["internet"],
            research: [],
            rankingPreference: ["QS-50"],
            preference_type: "ranking_focused"
        }
    },
    major_focused: {
        academic: {
            education: "master",
            school: "华中科技大学", 
            major: "人工智能",
            gpa: "85.0",
            gpaScale: "100",
            ranking: "20%"
        },
        intention: {
            countries: ["Hong Kong", "Singapore", "UK", "USA"],
            majors: ["computer_science", "data_science", "electrical_engineering"],
            duration: "2"
        },
        strength: {
            competition: ["provincial"],
            internship: ["internet", "other"],
            research: ["sci-paper"],
            rankingPreference: ["QS-100"],
            preference_type: "major_focused"
        }
    },
    balanced: {
        academic: {
            education: "master",
            school: "华南理工大学",
            major: "微电子科学与工程",
            gpa: "85.5",
            gpaScale: "100",
            ranking: "10%"
        },
        intention: {
            countries: ["Hong Kong", "Singapore"],
            majors: ["computer_science", "electrical_engineering"],
            duration: "1"
        },
        strength: {
            competition: [],
            internship: ["internet"],
            research: [],
            rankingPreference: ["QS-50", "QS-100"],
            preference_type: "balanced"
        }
    }
};
```

---

## 🚀 迁移指南

### 从v2.0升级到v3.0

#### 1. 必需的前端代码更改
- 在strength表单中添加`preference_type`选择器
- 更新数据提交逻辑，包含新字段
- 添加新的流式事件处理逻辑
- 更新候选池和推荐结果的显示逻辑

#### 2. 可选的UI增强
- 实现卡片式偏好选择器
- 添加权重配置可视化
- 实现快速测试用例功能

#### 3. 向后兼容性
- 如果前端不传`preference_type`，系统将使用默认值`"balanced"`
- 所有原有的API接口保持不变
- 原有的评分字段继续返回，新增`school_ranking_score`字段

---

## 📞 技术支持

### 常见问题

**Q: 如果用户没有选择偏好类型会怎样？**
A: 系统会自动使用`"balanced"`作为默认值，确保推荐功能正常工作。

**Q: 三种偏好类型的具体区别是什么？**
A: 主要体现在权重分配上，排名导向型会提高`school_ranking_score`权重至40%，专业导向型会提高`program_direction_match`权重至45%。

**Q: 是否需要更新现有的前端代码？**
A: 最少只需添加`preference_type`字段即可，其他功能都是增强性的，可以逐步实现。

### 联系方式
如有技术问题，请联系后端开发团队进行技术支持。

---

## 📈 预期效果

实现本功能后，用户将能够：
1. 根据自己的申请策略选择合适的偏好类型
2. 获得更加个性化的推荐结果
3. 清晰了解系统的推荐权重逻辑
4. 看到更详细的评分维度分析

系统的推荐准确性和用户满意度预期将显著提升。 