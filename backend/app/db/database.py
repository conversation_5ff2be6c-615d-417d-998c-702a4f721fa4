from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, declarative_base
from app.core.config import settings

# 创建异步引擎
engine = create_async_engine(
    settings.DATABASE_URL,
    echo=True,  # 设置为 True 会在控制台显示 SQL 语句，方便调试
    future=True,  # 使用 SQLAlchemy 2.0 特性
    connect_args={
        "server_settings": {
            "TimeZone": "Asia/Shanghai"  # 设置PostgreSQL连接使用上海时区，注意大小写
        }
    }
)

# 创建异步会话工厂
AsyncSessionLocal = sessionmaker(
    engine,
    class_=AsyncSession,
    expire_on_commit=False,  # 提交后不过期，允许在事务提交后仍使用对象
    autoflush=False
)

# 创建所有模型的基类
Base = declarative_base()

# 创建异步获取数据库会话的依赖函数
async def get_db():
    """
    获取异步数据库会话的依赖函数，用于 FastAPI 依赖注入
    """
    session = AsyncSessionLocal()
    try:
        yield session
        # 如果没有异常，则提交事务
        await session.commit()
    except Exception as e:
        # 如果有异常，则回滚事务
        await session.rollback()
        raise e
    finally:
        # 最后关闭会话
        await session.close()