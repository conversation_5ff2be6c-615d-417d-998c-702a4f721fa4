"""add embedding to cases table

Revision ID: add_embedding_cases
Revises: 
Create Date: 2024-06-18 16:30:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'add_embedding_cases'
down_revision = None
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Add embedding column to ai_selection_cases table"""
    # Add embedding column to cases table if it doesn't exist
    try:
        op.add_column('ai_selection_cases', 
                     sa.Column('embedding', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment="案例描述的向量嵌入"))
        print("Successfully added embedding column to ai_selection_cases table")
    except Exception as e:
        print(f"Note: embedding column might already exist: {e}")


def downgrade() -> None:
    """Remove embedding column from ai_selection_cases table"""
    try:
        op.drop_column('ai_selection_cases', 'embedding')
        print("Successfully removed embedding column from ai_selection_cases table")
    except Exception as e:
        print(f"Note: embedding column might not exist: {e}") 