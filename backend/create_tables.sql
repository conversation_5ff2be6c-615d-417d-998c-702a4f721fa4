-- 创建用户表
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(64) UNIQUE NOT NULL,
    email VARCHAR(120) UNIQUE NOT NULL,
    nickname VARCHAR(64),
    password_hash VARCHAR(128) NOT NULL,
    role VARCHAR(20) DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建触发器函数，用于自动更新 updated_at 字段
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 为 users 表添加触发器
CREATE TRIGGER update_users_updated_at
BEFORE UPDATE ON users
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 添加注释
COMMENT ON TABLE users IS '用户表，存储系统用户信息';
COMMENT ON COLUMN users.id IS '用户 ID，自增主键';
COMMENT ON COLUMN users.username IS '用户名，唯一，用于登录';
COMMENT ON COLUMN users.email IS '电子邮箱，唯一';
COMMENT ON COLUMN users.nickname IS '用户昵称，可选';
COMMENT ON COLUMN users.password_hash IS '密码哈希值';
COMMENT ON COLUMN users.role IS '用户角色，默认为普通用户(user)';
COMMENT ON COLUMN users.is_active IS '账户是否激活，默认为激活状态';
COMMENT ON COLUMN users.last_login IS '最后登录时间';
COMMENT ON COLUMN users.created_at IS '创建时间';
COMMENT ON COLUMN users.updated_at IS '更新时间';

-- 创建客户表
CREATE TABLE clients (
    id SERIAL PRIMARY KEY,
    id_hashed VARCHAR(16) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    gender VARCHAR(20),
    phone VARCHAR(50),
    email VARCHAR(120),
    location VARCHAR(100),
    address TEXT,
    id_card VARCHAR(50),
    passport VARCHAR(50),
    id_card_issuer VARCHAR(100),
    id_card_validity VARCHAR(100),
    passport_issue_place VARCHAR(100),
    passport_issue_date VARCHAR(50),
    passport_expiry VARCHAR(50),
    service_type VARCHAR(50) DEFAULT 'undergraduate',
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    is_archived BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 clients 表添加触发器
CREATE TRIGGER update_clients_updated_at
BEFORE UPDATE ON clients
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 创建索引
CREATE INDEX idx_clients_name ON clients(name);
CREATE INDEX idx_clients_user_id ON clients(user_id);
CREATE INDEX idx_clients_id_hashed ON clients(id_hashed);

-- 添加注释
COMMENT ON TABLE clients IS '客户表，存储客户基本信息';
COMMENT ON COLUMN clients.id IS '客户 ID，自增主键';
COMMENT ON COLUMN clients.id_hashed IS '客户哈希ID，用于前端展示和API调用';
COMMENT ON COLUMN clients.name IS '客户姓名';
COMMENT ON COLUMN clients.gender IS '性别：male(男)、female(女)、unknown(未知)';
COMMENT ON COLUMN clients.phone IS '电话号码';
COMMENT ON COLUMN clients.email IS '电子邮箱';
COMMENT ON COLUMN clients.location IS '所在城市';
COMMENT ON COLUMN clients.address IS '详细地址';
COMMENT ON COLUMN clients.id_card IS '身份证号码';
COMMENT ON COLUMN clients.passport IS '护照号码';
COMMENT ON COLUMN clients.id_card_issuer IS '身份证签发机构';
COMMENT ON COLUMN clients.id_card_validity IS '身份证有效期';
COMMENT ON COLUMN clients.passport_issue_place IS '护照签发地';
COMMENT ON COLUMN clients.passport_issue_date IS '护照签发日期';
COMMENT ON COLUMN clients.passport_expiry IS '护照过期日期';
COMMENT ON COLUMN clients.service_type IS '服务类型：undergraduate(本科)、master(硕士)等';
COMMENT ON COLUMN clients.user_id IS '关联的用户ID（顾问）';
COMMENT ON COLUMN clients.is_archived IS '是否已归档（服务完成）';
COMMENT ON COLUMN clients.created_at IS '创建时间';
COMMENT ON COLUMN clients.updated_at IS '更新时间';

-- 创建教育经历表
CREATE TABLE education (
    id SERIAL PRIMARY KEY,
    school VARCHAR(200) NOT NULL,
    major VARCHAR(200),
    degree VARCHAR(50),
    gpa VARCHAR(20),
    start_date VARCHAR(50),
    end_date VARCHAR(50),
    description TEXT,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    "order" INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 education 表添加触发器
CREATE TRIGGER update_education_updated_at
BEFORE UPDATE ON education
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 创建索引
CREATE INDEX idx_education_client_id ON education(client_id);

-- 添加注释
COMMENT ON TABLE education IS '教育经历表，存储客户的教育背景信息';
COMMENT ON COLUMN education.id IS '教育经历 ID，自增主键';
COMMENT ON COLUMN education.school IS '学校名称';
COMMENT ON COLUMN education.major IS '专业';
COMMENT ON COLUMN education.degree IS '学位：bachelor(本科)、master(硕士)、phd(博士)等';
COMMENT ON COLUMN education.gpa IS 'GPA成绩';
COMMENT ON COLUMN education.start_date IS '开始日期';
COMMENT ON COLUMN education.end_date IS '结束日期';
COMMENT ON COLUMN education.description IS '描述信息';
COMMENT ON COLUMN education.client_id IS '关联的客户ID';
COMMENT ON COLUMN education."order" IS '排序顺序';
COMMENT ON COLUMN education.created_at IS '创建时间';
COMMENT ON COLUMN education.updated_at IS '更新时间';

-- 创建学术经历表
CREATE TABLE academic (
    id SERIAL PRIMARY KEY,
    title VARCHAR(200) NOT NULL,
    type VARCHAR(200),
    date VARCHAR(50),
    description TEXT,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    "order" INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 academic 表添加触发器
CREATE TRIGGER update_academic_updated_at
BEFORE UPDATE ON academic
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 创建索引
CREATE INDEX idx_academic_client_id ON academic(client_id);

-- 添加注释
COMMENT ON TABLE academic IS '学术经历表，存储客户的学术经历信息';
COMMENT ON COLUMN academic.id IS '学术经历 ID，自增主键';
COMMENT ON COLUMN academic.title IS '项目主题';
COMMENT ON COLUMN academic.type IS '项目类型：毕业设计、论文、科研项目、学科课程项目、大学生创业项目、其他等';
COMMENT ON COLUMN academic.date IS '研究日期';
COMMENT ON COLUMN academic.description IS '详细描述';
COMMENT ON COLUMN academic.client_id IS '关联的客户ID';
COMMENT ON COLUMN academic."order" IS '排序顺序';
COMMENT ON COLUMN academic.created_at IS '创建时间';
COMMENT ON COLUMN academic.updated_at IS '更新时间';

-- 创建工作经历表
CREATE TABLE work (
    id SERIAL PRIMARY KEY,
    company VARCHAR(200) NOT NULL,
    position VARCHAR(200),
    start_date VARCHAR(50),
    end_date VARCHAR(50),
    description TEXT,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    "order" INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 work 表添加触发器
CREATE TRIGGER update_work_updated_at
BEFORE UPDATE ON work
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 创建索引
CREATE INDEX idx_work_client_id ON work(client_id);

-- 添加注释
COMMENT ON TABLE work IS '工作经历表，存储客户的工作/实习经历信息';
COMMENT ON COLUMN work.id IS '工作经历 ID，自增主键';
COMMENT ON COLUMN work.company IS '公司/单位名称';
COMMENT ON COLUMN work.position IS '职位';
COMMENT ON COLUMN work.start_date IS '开始日期';
COMMENT ON COLUMN work.end_date IS '结束日期';
COMMENT ON COLUMN work.description IS '工作描述';
COMMENT ON COLUMN work.client_id IS '关联的客户ID';
COMMENT ON COLUMN work."order" IS '排序顺序';
COMMENT ON COLUMN work.created_at IS '创建时间';
COMMENT ON COLUMN work.updated_at IS '更新时间';

-- 创建课外活动表
CREATE TABLE activities (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    role VARCHAR(100),
    start_date VARCHAR(50),
    end_date VARCHAR(50),
    description TEXT,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    "order" INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 activities 表添加触发器
CREATE TRIGGER update_activities_updated_at
BEFORE UPDATE ON activities
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 创建索引
CREATE INDEX idx_activities_client_id ON activities(client_id);

-- 添加注释
COMMENT ON TABLE activities IS '课外活动表，存储客户的课外活动信息';
COMMENT ON COLUMN activities.id IS '活动 ID，自增主键';
COMMENT ON COLUMN activities.name IS '活动名称';
COMMENT ON COLUMN activities.role IS '角色/职位';
COMMENT ON COLUMN activities.start_date IS '开始日期';
COMMENT ON COLUMN activities.end_date IS '结束日期';
COMMENT ON COLUMN activities.description IS '活动描述';
COMMENT ON COLUMN activities.client_id IS '关联的客户ID';
COMMENT ON COLUMN activities."order" IS '排序顺序';
COMMENT ON COLUMN activities.created_at IS '创建时间';
COMMENT ON COLUMN activities.updated_at IS '更新时间';

-- 创建奖项荣誉表
CREATE TABLE awards (
    id SERIAL PRIMARY KEY,
    name VARCHAR(200) NOT NULL,
    level VARCHAR(100),
    date VARCHAR(50),
    description TEXT,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    "order" INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 awards 表添加触发器
CREATE TRIGGER update_awards_updated_at
BEFORE UPDATE ON awards
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 创建索引
CREATE INDEX idx_awards_client_id ON awards(client_id);

-- 添加注释
COMMENT ON TABLE awards IS '奖项荣誉表，存储客户获得的奖项和荣誉信息';
COMMENT ON COLUMN awards.id IS '奖项 ID，自增主键';
COMMENT ON COLUMN awards.name IS '奖项名称';
COMMENT ON COLUMN awards.level IS '奖项级别';
COMMENT ON COLUMN awards.date IS '获奖日期';
COMMENT ON COLUMN awards.description IS '奖项描述';
COMMENT ON COLUMN awards.client_id IS '关联的客户ID';
COMMENT ON COLUMN awards."order" IS '排序顺序';
COMMENT ON COLUMN awards.created_at IS '创建时间';
COMMENT ON COLUMN awards.updated_at IS '更新时间';

-- 创建技能表
CREATE TABLE skills (
    id SERIAL PRIMARY KEY,
    type VARCHAR(100) NOT NULL,
    description TEXT,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    "order" INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 skills 表添加触发器
CREATE TRIGGER update_skills_updated_at
BEFORE UPDATE ON skills
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 创建索引
CREATE INDEX idx_skills_client_id ON skills(client_id);

-- 添加注释
COMMENT ON TABLE skills IS '技能表，存储客户的技能信息';
COMMENT ON COLUMN skills.id IS '技能 ID，自增主键';
COMMENT ON COLUMN skills.type IS '技能类型，如"专业技能"、"综合技能"等';
COMMENT ON COLUMN skills.description IS '技能描述，具体的技能内容';
COMMENT ON COLUMN skills.client_id IS '关联的客户ID';
COMMENT ON COLUMN skills."order" IS '排序顺序';
COMMENT ON COLUMN skills.created_at IS '创建时间';
COMMENT ON COLUMN skills.updated_at IS '更新时间';

-- 创建语言成绩表
CREATE TABLE language_scores (
    id SERIAL PRIMARY KEY,
    type VARCHAR(50) NOT NULL,
    score VARCHAR(50) NOT NULL,
    date VARCHAR(50),
    validity VARCHAR(50),
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    "order" INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 language_scores 表添加触发器
CREATE TRIGGER update_language_scores_updated_at
BEFORE UPDATE ON language_scores
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 创建索引
CREATE INDEX idx_language_scores_client_id ON language_scores(client_id);

-- 添加注释
COMMENT ON TABLE language_scores IS '语言成绩表，存储客户的语言考试成绩信息';
COMMENT ON COLUMN language_scores.id IS '语言成绩 ID，自增主键';
COMMENT ON COLUMN language_scores.type IS '考试类型：toefl、ielts、gre、gmat等';
COMMENT ON COLUMN language_scores.score IS '分数';
COMMENT ON COLUMN language_scores.date IS '考试日期';
COMMENT ON COLUMN language_scores.validity IS '有效期';
COMMENT ON COLUMN language_scores.client_id IS '关联的客户ID';
COMMENT ON COLUMN language_scores."order" IS '排序顺序';
COMMENT ON COLUMN language_scores.created_at IS '创建时间';
COMMENT ON COLUMN language_scores.updated_at IS '更新时间';

-- 创建个人想法表
CREATE TABLE thoughts (
    id SERIAL PRIMARY KEY,
    target_major TEXT,
    personal_understanding TEXT,
    academic_match TEXT,
    work_match TEXT,
    future_plan TEXT,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 thoughts 表添加触发器
CREATE TRIGGER update_thoughts_updated_at
BEFORE UPDATE ON thoughts
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 创建索引
CREATE INDEX idx_thoughts_client_id ON thoughts(client_id);

-- 添加注释
COMMENT ON TABLE thoughts IS '个人想法表，存储客户的个人想法信息';
COMMENT ON COLUMN thoughts.id IS '想法 ID，自增主键';
COMMENT ON COLUMN thoughts.target_major IS '目标专业申请动机';
COMMENT ON COLUMN thoughts.personal_understanding IS '专业个人解读';
COMMENT ON COLUMN thoughts.academic_match IS '学术经历匹配';
COMMENT ON COLUMN thoughts.work_match IS '工作经历匹配';
COMMENT ON COLUMN thoughts.future_plan IS '未来规划';
COMMENT ON COLUMN thoughts.client_id IS '关联的客户ID';
COMMENT ON COLUMN thoughts.created_at IS '创建时间';
COMMENT ON COLUMN thoughts.updated_at IS '更新时间';

-- 创建背景自定义模块表
CREATE TABLE background_custom_modules (
    id SERIAL PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    content TEXT,
    "order" INTEGER DEFAULT 0,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 background_custom_modules 表添加触发器
CREATE TRIGGER update_background_custom_modules_updated_at
BEFORE UPDATE ON background_custom_modules
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 创建索引
CREATE INDEX idx_background_custom_modules_client_id ON background_custom_modules(client_id);

-- 添加注释
COMMENT ON TABLE background_custom_modules IS '背景自定义模块表，存储客户的背景模块信息';
COMMENT ON COLUMN background_custom_modules.id IS '背景模块 ID，自增主键';
COMMENT ON COLUMN background_custom_modules.title IS '模块标题';
COMMENT ON COLUMN background_custom_modules.content IS '模块内容';
COMMENT ON COLUMN background_custom_modules."order" IS '排序顺序';
COMMENT ON COLUMN background_custom_modules.client_id IS '关联的客户ID';
COMMENT ON COLUMN background_custom_modules.created_at IS '创建时间';
COMMENT ON COLUMN background_custom_modules.updated_at IS '更新时间';

-- 创建想法自定义模块表
CREATE TABLE thought_custom_modules (
    id SERIAL PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    content TEXT,
    "order" INTEGER DEFAULT 0,
    client_id INTEGER NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 为 thought_custom_modules 表添加触发器
CREATE TRIGGER update_thought_custom_modules_updated_at
BEFORE UPDATE ON thought_custom_modules
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- 创建索引
CREATE INDEX idx_thought_custom_modules_client_id ON thought_custom_modules(client_id);

-- 添加注释
COMMENT ON TABLE thought_custom_modules IS '想法自定义模块表，存储客户的想法模块信息';
COMMENT ON COLUMN thought_custom_modules.id IS '想法模块 ID，自增主键';
COMMENT ON COLUMN thought_custom_modules.title IS '模块标题';
COMMENT ON COLUMN thought_custom_modules.content IS '模块内容';
COMMENT ON COLUMN thought_custom_modules."order" IS '排序顺序';
COMMENT ON COLUMN thought_custom_modules.client_id IS '关联的客户ID';
COMMENT ON COLUMN thought_custom_modules.created_at IS '创建时间';
COMMENT ON COLUMN thought_custom_modules.updated_at IS '更新时间';